package com.example.warehousemanagement.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.warehousemanagement.databinding.ItemScannedTagBinding;
import com.example.warehousemanagement.model.RfidTag;
import com.example.warehousemanagement.utils.DateUtils;
import java.util.List;

/**
 * 回收标签适配器
 * 用于显示回收页面扫描到的RFID标签列表，包含保质期信息
 */
public class RecoveryTagAdapter extends RecyclerView.Adapter<RecoveryTagAdapter.RecoveryTagViewHolder> {
    
    private List<RfidTag> tagList;
    private OnDeleteClickListener onDeleteClickListener;
    
    public interface OnDeleteClickListener {
        void onDeleteClick(int position);
    }
    
    public RecoveryTagAdapter(List<RfidTag> tagList) {
        this.tagList = tagList;
    }
    
    public void setOnDeleteClickListener(OnDeleteClickListener listener) {
        this.onDeleteClickListener = listener;
    }
    
    @NonNull
    @Override
    public RecoveryTagViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemScannedTagBinding binding = ItemScannedTagBinding.inflate(
            LayoutInflater.from(parent.getContext()), parent, false);
        return new RecoveryTagViewHolder(binding);
    }
    
    @Override
    public void onBindViewHolder(@NonNull RecoveryTagViewHolder holder, int position) {
        RfidTag tag = tagList.get(position);
        holder.bind(tag, position);
    }
    
    @Override
    public int getItemCount() {
        return tagList.size();
    }
    
    class RecoveryTagViewHolder extends RecyclerView.ViewHolder {
        private ItemScannedTagBinding binding;
        
        public RecoveryTagViewHolder(ItemScannedTagBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
        
        public void bind(RfidTag tag, int position) {
            // 显示标签ID
            binding.tvTagId.setText(tag.getTagId());
            
            // 构建详细信息
            StringBuilder detailInfo = new StringBuilder();
            
            // 显示客户信息
            if (tag.getCustomerName() != null && !tag.getCustomerName().isEmpty()) {
                detailInfo.append("客户: ").append(tag.getCustomerName()).append("\n");
            }
            
            // 显示发货日期
            if (tag.getShippingDate() != null) {
                detailInfo.append("发货日期: ").append(DateUtils.formatDate(tag.getShippingDate())).append("\n");
            }
            
            // 显示保质期信息
            if (tag.getExpiryDays() > 0) {
                detailInfo.append("保质期: ").append(tag.getExpiryDays()).append("天\n");
                detailInfo.append("已使用: ").append(tag.getUsedDays()).append("天\n");
                detailInfo.append("剩余: ").append(tag.getRemainingDays()).append("天\n");
                detailInfo.append("需要回收: ").append(tag.isNeedRecovery() ? "是" : "否");
            } else {
                detailInfo.append("状态: ").append(tag.getTagStatus() != null ? tag.getTagStatus() : "未知");
            }
            
            binding.tvScanTime.setText(detailInfo.toString());
            
            // 根据是否需要回收设置文本颜色
            if (tag.isNeedRecovery()) {
                binding.tvScanTime.setTextColor(
                    binding.getRoot().getContext().getColor(android.R.color.holo_red_dark));
            } else {
                binding.tvScanTime.setTextColor(
                    binding.getRoot().getContext().getColor(android.R.color.darker_gray));
            }
            
            // 设置删除按钮点击事件
            binding.btnDelete.setOnClickListener(v -> {
                if (onDeleteClickListener != null) {
                    onDeleteClickListener.onDeleteClick(position);
                }
            });
        }
    }
}
