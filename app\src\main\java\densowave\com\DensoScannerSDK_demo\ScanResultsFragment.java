package densowave.com.DensoScannerSDK_demo;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.warehousemanagement.R;

public class ScanResultsFragment extends Fragment {
    private TextView tvScanResults;

    public static ScanResultsFragment newInstance() {
        return new ScanResultsFragment();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_scan_results, container, false);
        tvScanResults = view.findViewById(R.id.tv_scan_results);
        return view;
    }

    public void updateScanResults(String results) {
        if (tvScanResults != null) {
            tvScanResults.setText(results);
        }
    }

    public String getScanResults() {
        if (tvScanResults != null) {
            return tvScanResults.getText().toString();
        }
        return "";
    }
}