// Generated by view binder compiler. Do not edit!
package com.example.warehousemanagement.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.warehousemanagement.R;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.slider.Slider;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySettingsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnSaveSettings;

  @NonNull
  public final TextInputEditText etServerUrl;

  @NonNull
  public final TextInputEditText etSyncInterval;

  @NonNull
  public final TextInputEditText etTimeout;

  @NonNull
  public final Slider sliderScanPower;

  @NonNull
  public final SwitchMaterial switchAutoConnect;

  @NonNull
  public final SwitchMaterial switchAutoSync;

  @NonNull
  public final MaterialToolbar toolbar;

  private ActivitySettingsBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton btnSaveSettings, @NonNull TextInputEditText etServerUrl,
      @NonNull TextInputEditText etSyncInterval, @NonNull TextInputEditText etTimeout,
      @NonNull Slider sliderScanPower, @NonNull SwitchMaterial switchAutoConnect,
      @NonNull SwitchMaterial switchAutoSync, @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.btnSaveSettings = btnSaveSettings;
    this.etServerUrl = etServerUrl;
    this.etSyncInterval = etSyncInterval;
    this.etTimeout = etTimeout;
    this.sliderScanPower = sliderScanPower;
    this.switchAutoConnect = switchAutoConnect;
    this.switchAutoSync = switchAutoSync;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_save_settings;
      MaterialButton btnSaveSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnSaveSettings == null) {
        break missingId;
      }

      id = R.id.et_server_url;
      TextInputEditText etServerUrl = ViewBindings.findChildViewById(rootView, id);
      if (etServerUrl == null) {
        break missingId;
      }

      id = R.id.et_sync_interval;
      TextInputEditText etSyncInterval = ViewBindings.findChildViewById(rootView, id);
      if (etSyncInterval == null) {
        break missingId;
      }

      id = R.id.et_timeout;
      TextInputEditText etTimeout = ViewBindings.findChildViewById(rootView, id);
      if (etTimeout == null) {
        break missingId;
      }

      id = R.id.slider_scan_power;
      Slider sliderScanPower = ViewBindings.findChildViewById(rootView, id);
      if (sliderScanPower == null) {
        break missingId;
      }

      id = R.id.switch_auto_connect;
      SwitchMaterial switchAutoConnect = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoConnect == null) {
        break missingId;
      }

      id = R.id.switch_auto_sync;
      SwitchMaterial switchAutoSync = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoSync == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivitySettingsBinding((LinearLayout) rootView, btnSaveSettings, etServerUrl,
          etSyncInterval, etTimeout, sliderScanPower, switchAutoConnect, switchAutoSync, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
