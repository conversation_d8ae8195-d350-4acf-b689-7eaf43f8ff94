package com.example.warehousemanagement.model;

import java.util.Date;

/**
 * RFID标签模型
 */
public class RfidTag {
    private long id;
    private String tagId;
    private String tagType;
    private String tagStatus;
    private Date storageDate;
    private Date shippingDate;
    private Date recoveryDate;
    private String customerName;
    private String remark1;
    private String remark2;
    private String remark3;
    private String remark4;
    private String remark5;
    
    // 计算字段
    private int expiryDays;
    private int usedDays;
    private int remainingDays;
    private boolean needRecovery;
    
    public long getId() {
        return id;
    }
    
    public void setId(long id) {
        this.id = id;
    }
    
    public String getTagId() {
        return tagId;
    }
    
    public void setTagId(String tagId) {
        this.tagId = tagId;
    }
    
    public String getTagType() {
        return tagType;
    }
    
    public void setTagType(String tagType) {
        this.tagType = tagType;
    }
    
    public String getTagStatus() {
        return tagStatus;
    }
    
    public void setTagStatus(String tagStatus) {
        this.tagStatus = tagStatus;
    }
    
    public Date getStorageDate() {
        return storageDate;
    }
    
    public void setStorageDate(Date storageDate) {
        this.storageDate = storageDate;
    }
    
    public Date getShippingDate() {
        return shippingDate;
    }
    
    public void setShippingDate(Date shippingDate) {
        this.shippingDate = shippingDate;
    }
    
    public Date getRecoveryDate() {
        return recoveryDate;
    }
    
    public void setRecoveryDate(Date recoveryDate) {
        this.recoveryDate = recoveryDate;
    }
    
    public String getCustomerName() {
        return customerName;
    }
    
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
    
    public String getRemark1() {
        return remark1;
    }
    
    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }
    
    public String getRemark2() {
        return remark2;
    }
    
    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }
    
    public String getRemark3() {
        return remark3;
    }
    
    public void setRemark3(String remark3) {
        this.remark3 = remark3;
    }
    
    public String getRemark4() {
        return remark4;
    }
    
    public void setRemark4(String remark4) {
        this.remark4 = remark4;
    }
    
    public String getRemark5() {
        return remark5;
    }
    
    public void setRemark5(String remark5) {
        this.remark5 = remark5;
    }
    
    public int getExpiryDays() {
        return expiryDays;
    }
    
    public void setExpiryDays(int expiryDays) {
        this.expiryDays = expiryDays;
    }
    
    public int getUsedDays() {
        return usedDays;
    }
    
    public void setUsedDays(int usedDays) {
        this.usedDays = usedDays;
    }
    
    public int getRemainingDays() {
        return remainingDays;
    }
    
    public void setRemainingDays(int remainingDays) {
        this.remainingDays = remainingDays;
    }
    
    public boolean isNeedRecovery() {
        return needRecovery;
    }
    
    public void setNeedRecovery(boolean needRecovery) {
        this.needRecovery = needRecovery;
    }
}
