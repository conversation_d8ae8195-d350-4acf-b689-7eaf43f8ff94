# RFID防火阻燃箱/袋管理系统设计方案

## 目录

- [1. 设计需求](#1-设计需求)
- [2. 系统架构](#2-系统架构)
- [3. 前端设计](#3-前端设计)
  - [3.1 界面设计](#31-界面设计)
  - [3.2 功能流程图](#32-功能流程图)
- [4. 后端设计](#4-后端设计)
  - [4.1 数据库设计](#41-数据库设计)
  - [4.2 API接口设计](#42-api接口设计)
- [5. 前后端交互流程](#5-前后端交互流程)
- [6. 安全性设计](#6-安全性设计)
- [7. 扩展性考虑](#7-扩展性考虑)

## 1. 设计需求

根据原始需求文档，系统主要功能包括：

1. **用户管理**：登录验证、权限控制
2. **RFID设备控制**：自动开启/关闭RFID设备
3. **产品管理**：
   - 产品入库：记录标签ID、状态、入库日期等信息
   - 产品发货：更新标签状态、记录发货信息
   - 产品回收：更新标签状态、记录回收信息
4. **系统设置**：消息通知、保质期设置、扫描距离设置

## 2. 系统架构

系统采用前后端分离架构：

- **前端**：Android原生应用，负责UI展示和RFID设备控制
- **后端**：RESTful API服务，负责数据处理和业务逻辑
- **数据库**：MySQL关系型数据库，存储系统数据

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Android端  │<────>│  后端服务   │<────>│  数据库     │
└─────────────┘      └─────────────┘      └─────────────┘
```

## 3. 前端设计

### 3.1 界面设计

#### 登录界面
- 用户名/密码输入框
- 登录按钮
- 错误提示区域

#### 主界面（TabBar导航）
- 入库标签页
- 发货标签页
- 回收标签页
- 我的标签页

#### 入库界面
- 查询条件区域（入库日期、备注、物品类型筛选）
- 记录列表
- 添加新记录按钮

#### 入库添加界面
- 已扫描标签列表（标签ID、入库日期）
- 扫描按钮
- 删除操作
- 保存按钮

#### 发货界面
- 查询条件区域（发货日期、客户名称、备注、物品类型筛选）
- 记录列表
- 添加新记录按钮

#### 发货添加界面
- 已扫描标签列表（标签ID、入库日期）
- 客户名称输入
- 扫描按钮
- 删除操作
- 保存按钮

#### 回收界面
- 查询条件区域（回收日期、客户名称、备注、物品类型筛选）
- 记录列表
- 添加新记录按钮

#### 回收添加界面
- 已扫描标签列表（标签ID、客户、发货日期、保质期、已使用天数、是否需要回收、可用天数）
- 扫描按钮
- 删除操作
- 保存按钮

#### 我的界面
- 用户信息显示
- 退出登录按钮
- 设置选项：
  - 消息通知开关
  - 保质期设置（箱子/袋子）
  - 回收天数设置
  - 扫描识别距离设置（1-3米）

### 3.2 功能流程图

#### 登录流程

```mermaid
graph TD
    A[开始] --> B[输入用户名密码]
    B --> C{验证}
    C -->|成功| D[进入主界面]
    C -->|失败| E[显示错误信息]
    E --> B
    D --> F[自动开启RFID设备]
```

#### 入库流程

```mermaid
graph TD
    A[入库界面] --> B[查看已入库记录]
    A --> C[添加新入库记录]
    C --> D[扫描RFID标签]
    D --> E{标签是否已存在}
    E -->|是| F[提示已存在]
    E -->|否| G[添加到列表]
    G --> H{继续扫描}
    H -->|是| D
    H -->|否| I[保存入库记录]
    I --> J[更新数据库]
    J --> K[返回入库界面]
```

#### 发货流程

```mermaid
graph TD
    A[发货界面] --> B[查看已发货记录]
    A --> C[添加新发货记录]
    C --> D[扫描RFID标签]
    D --> E{标签是否已存在}
    E -->|是| F[提示已存在]
    E -->|否| G[添加到列表]
    G --> H{继续扫描}
    H -->|是| D
    H -->|否| I[输入客户信息]
    I --> J[保存发货记录]
    J --> K[更新数据库]
    K --> L[返回发货界面]
```

#### 回收流程

```mermaid
graph TD
    A[回收界面] --> B[查看已回收记录]
    A --> C[添加新回收记录]
    C --> D[扫描RFID标签]
    D --> E{标签是否已存在}
    E -->|是| F[提示已存在]
    E -->|否| G[添加到列表并计算保质期信息]
    G --> H{继续扫描}
    H -->|是| D
    H -->|否| I[保存回收记录]
    I --> J[更新数据库]
    J --> K[返回回收界面]
```

## 4. 后端设计

### 4.1 数据库设计

#### 用户表 (users)

```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,  -- 存储加密后的密码
    real_name VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    role VARCHAR(20) NOT NULL DEFAULT 'user',  -- 用户角色：admin, user
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 标签表 (rfid_tags)

```sql
CREATE TABLE `cms_rfid_tags` (
	`id` BIGINT(20) NOT NULL AUTO_INCREMENT,
	`createDate` DATETIME NOT NULL COMMENT '创建日期',
	`updateDate` DATETIME NOT NULL COMMENT '更新日期',
	`ownerId` BIGINT(20) NOT NULL DEFAULT '0' COMMENT '创建人',
	`dispatchId` VARCHAR(500) NOT NULL DEFAULT '' COLLATE 'utf8_general_ci',
	`recordStatus` INT(11) NOT NULL DEFAULT '1',
	`flowStatus` VARCHAR(50) NOT NULL DEFAULT '' COLLATE 'utf8_general_ci',
	`TagID` VARCHAR(255) NULL DEFAULT NULL COMMENT 'tagID' COLLATE 'utf8_general_ci',
	`tagType` VARCHAR(255) NULL DEFAULT NULL COMMENT 'Tag类型' COLLATE 'utf8_general_ci',
	`tagStatus` VARCHAR(255) NULL DEFAULT NULL COMMENT 'Tag状态' COLLATE 'utf8_general_ci',
	`storageDate` DATETIME NULL DEFAULT NULL COMMENT '入库日期',
	`shippingDate` DATETIME NULL DEFAULT NULL COMMENT '发货日期',
	`recoveryDate` DATETIME NULL DEFAULT NULL COMMENT '回收日期',
	`customerName` VARCHAR(255) NULL DEFAULT NULL COMMENT '客户名称' COLLATE 'utf8_general_ci',
	`remark1` VARCHAR(255) NULL DEFAULT NULL COMMENT '备注1' COLLATE 'utf8_general_ci',
	`remark2` VARCHAR(255) NULL DEFAULT NULL COMMENT '备注2' COLLATE 'utf8_general_ci',
	`remark3` VARCHAR(255) NULL DEFAULT NULL COMMENT '备注3' COLLATE 'utf8_general_ci',
	`remark4` VARCHAR(255) NULL DEFAULT NULL COMMENT '备注4' COLLATE 'utf8_general_ci',
	`remark5` VARCHAR(255) NULL DEFAULT NULL COMMENT '备注5' COLLATE 'utf8_general_ci',
	PRIMARY KEY (`id`) USING BTREE
)
COLLATE='utf8_general_ci'
ENGINE=InnoDB
;
```

#### 系统设置表 (settings)

```sql
CREATE TABLE `cms_rfid_settings` (
	`id` BIGINT(20) NOT NULL AUTO_INCREMENT,
	`createDate` DATETIME NOT NULL COMMENT '创建日期',
	`updateDate` DATETIME NOT NULL COMMENT '更新日期',
	`ownerId` BIGINT(20) NOT NULL DEFAULT '0' COMMENT '创建人',
	`dispatchId` VARCHAR(500) NOT NULL DEFAULT '' COLLATE 'utf8_general_ci',
	`recordStatus` INT(11) NOT NULL DEFAULT '1',
	`flowStatus` VARCHAR(50) NOT NULL DEFAULT '' COLLATE 'utf8_general_ci',
	`userID` BIGINT(20) NULL DEFAULT '0' COMMENT '用户',
	`notificationEnabled` BIGINT(20) NULL DEFAULT '0' COMMENT '是否启用消息通知',
	`boxExpiryDays` BIGINT(20) NULL DEFAULT '0' COMMENT '箱子质保期',
	`bagExpiryDays` BIGINT(20) NULL DEFAULT '0' COMMENT '袋子质保期',
	`recoveryDays` BIGINT(20) NULL DEFAULT '0' COMMENT '回收提醒天数',
	`scanDistance` BIGINT(20) NULL DEFAULT '0' COMMENT '扫描距离',
	PRIMARY KEY (`id`) USING BTREE
)
COLLATE='utf8_general_ci'
ENGINE=InnoDB
;

```

#### 操作日志表 (operation_logs)

```sql
CREATE TABLE operation_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    operation_type VARCHAR(50) NOT NULL,  -- 操作类型：登录、入库、发货、回收等
    operation_content TEXT,  -- 操作内容
    operation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(50),  -- 操作IP
    device_info VARCHAR(255),  -- 设备信息
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 4.2 API接口设计

#### 接口地址
- 基础地址：`127.0.0.1`

#### 用户认证接口

```
POST /api/auth/login
请求参数：
{
    "username": "用户名",
    "password": "密码"
}

响应：
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "user": {
            "id": 1,
            "username": "用户名",
            "real_name": "真实姓名",
            "role": "角色"
        }
    }
}
```

```
POST /api/auth/logout

响应：
{
    "code": 200,
    "message": "退出成功"
}
```

#### 标签管理接口

##### 入库相关

```
GET /api/tags/storage
请求参数：
{
    "storage_date_start": "开始日期",  // 可选
    "storage_date_end": "结束日期",    // 可选
    "tag_type": "x或d",              // 可选
    "remark": "备注关键词"            // 可选
}

响应：
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "total": 100,
        "list": [
            {
                "id": 1,
                "tag_id": "hkya00001x",
                "tag_type": "x",
                "status": "入库",
                "storage_date": "2023-01-01",
                "remark1": "备注1"
            }
        ]
    }
}
```

```
POST /api/tags/storage

请求参数：
{
    "tags": [
        {
            "tag_id": "hkya00001x",
            "storage_date": "2023-01-01",
            "remark1": "备注1",
            "remark2": "备注2"
        }
    ]
}

响应：
{
    "code": 200,
    "message": "入库成功",
    "data": {
        "success_count": 1,
        "failed_count": 0,
        "failed_tags": []
    }
}
```

##### 发货相关

```
GET /api/tags/shipping
请求参数：
{
    "shipping_date_start": "开始日期",  // 可选
    "shipping_date_end": "结束日期",    // 可选
    "customer_name": "客户名称",       // 可选
    "tag_type": "x或d",               // 可选
    "remark": "备注关键词"             // 可选
}

响应：
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "total": 100,
        "list": [
            {
                "id": 1,
                "tag_id": "hkya00001x",
                "tag_type": "x",
                "status": "发货",
                "storage_date": "2023-01-01",
                "shipping_date": "2023-01-15",
                "customer_name": "客户A",
                "remark1": "备注1"
            }
        ]
    }
}
```

```
POST /api/tags/shipping

请求参数：
{
    "customer_name": "客户A",
    "shipping_date": "2023-01-15",
    "tags": [
        {
            "tag_id": "hkya00001x",
            "remark1": "备注1",
            "remark2": "备注2"
        }
    ]
}

响应：
{
    "code": 200,
    "message": "发货成功",
    "data": {
        "success_count": 1,
        "failed_count": 0,
        "failed_tags": []
    }
}
```

##### 回收相关

```
GET /api/tags/recovery
请求参数：
{
    "recovery_date_start": "开始日期",  // 可选
    "recovery_date_end": "结束日期",    // 可选
    "customer_name": "客户名称",       // 可选
    "tag_type": "x或d",               // 可选
    "remark": "备注关键词"             // 可选
}

响应：
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "total": 100,
        "list": [
            {
                "id": 1,
                "tag_id": "hkya00001x",
                "tag_type": "x",
                "status": "回收",
                "storage_date": "2023-01-01",
                "shipping_date": "2023-01-15",
                "recovery_date": "2023-06-15",
                "customer_name": "客户A",
                "remark1": "备注1"
            }
        ]
    }
}
```

```
POST /api/tags/recovery

请求参数：
{
    "recovery_date": "2023-06-15",
    "tags": [
        {
            "tag_id": "hkya00001x",
            "remark1": "备注1",
            "remark2": "备注2"
        }
    ]
}

响应：
{
    "code": 200,
    "message": "回收成功",
    "data": {
        "success_count": 1,
        "failed_count": 0,
        "failed_tags": []
    }
}
```

##### 标签信息查询

```
GET /api/tags/info/{tag_id}

响应：
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "tag_id": "hkya00001x",
        "tag_type": "x",
        "status": "发货",
        "storage_date": "2023-01-01",
        "shipping_date": "2023-01-15",
        "recovery_date": null,
        "customer_name": "客户A",
        "expiry_days": 365,  // 保质期天数
        "used_days": 180,    // 已使用天数
        "remaining_days": 185,  // 剩余天数
        "need_recovery": false,  // 是否需要回收
        "remark1": "备注1",
        "remark2": "备注2"
    }
}
```

#### 系统设置接口

```
GET /api/settings

响应：
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "notification_enabled": true,
        "box_expiry_days": 365,
        "bag_expiry_days": 180,
        "recovery_days": 30,
        "scan_distance": 2
    }
}
```

```
POST /api/settings

请求参数：
{
    "notification_enabled": true,
    "box_expiry_days": 365,
    "bag_expiry_days": 180,
    "recovery_days": 30,
    "scan_distance": 2
}

响应：
{
    "code": 200,
    "message": "设置成功"
}
```

## 5. 前后端交互流程

### 登录认证流程

```mermaid
sequenceDiagram
    participant A as Android客户端
    participant S as 服务端
    participant D as 数据库
    
    A->>S: 发送登录请求(用户名、密码)
    S->>D: 验证用户信息
    D-->>S: 返回验证结果
    alt 验证成功
        S->>S: 创建HttpSession并存储用户信息
        S-->>A: 返回用户信息和会话Cookie
        A->>A: 存储会话Cookie，开启RFID设备
    else 验证失败
        S-->>A: 返回错误信息
    end
```

### 入库操作流程

```mermaid
sequenceDiagram
    participant A as Android客户端
    participant S as 服务端
    participant D as 数据库
    
    A->>A: 扫描RFID标签
    A->>A: 解析标签信息
    A->>A: 添加到临时列表
    A->>S: 发送入库请求(标签列表)
    S->>D: 检查标签是否已存在
    D-->>S: 返回检查结果
    S->>D: 保存入库记录
    D-->>S: 返回保存结果
    S-->>A: 返回入库结果
    A->>A: 显示操作结果
```

### 发货操作流程

```mermaid
sequenceDiagram
    participant A as Android客户端
    participant S as 服务端
    participant D as 数据库
    
    A->>A: 扫描RFID标签
    A->>A: 解析标签信息
    A->>A: 添加到临时列表
    A->>S: 发送发货请求(标签列表、客户信息)
    S->>D: 检查标签状态
    D-->>S: 返回检查结果
    S->>D: 更新标签状态为发货
    D-->>S: 返回更新结果
    S-->>A: 返回发货结果
    A->>A: 显示操作结果
```

### 回收操作流程

```mermaid
sequenceDiagram
    participant A as Android客户端
    participant S as 服务端
    participant D as 数据库
    
    A->>A: 扫描RFID标签
    A->>A: 解析标签信息
    A->>S: 获取标签详细信息
    S->>D: 查询标签信息
    D-->>S: 返回标签信息
    S-->>A: 返回标签详情(含保质期信息)
    A->>A: 添加到临时列表并显示
    A->>S: 发送回收请求(标签列表)
    S->>D: 更新标签状态为回收
    D-->>S: 返回更新结果
    S-->>A: 返回回收结果
    A->>A: 显示操作结果
```

### 设置更新流程

```mermaid
sequenceDiagram
    participant A as Android客户端
    participant S as 服务端
    participant D as 数据库
    
    A->>S: 获取当前设置
    S->>D: 查询用户设置
    D-->>S: 返回设置信息
    S-->>A: 返回设置数据
    A->>A: 显示设置界面
    A->>S: 提交更新后的设置
    S->>D: 更新设置数据
    D-->>S: 返回更新结果
    S-->>A: 返回设置更新结果
    A->>A: 显示操作结果
```

## 6. 安全性设计

1. **用户认证**：采用HttpSession进行身份验证和会话管理
2. **密码安全**：密码使用bcrypt等算法加密存储
3. **数据传输**：使用HTTPS协议确保数据传输安全
4. **权限控制**：基于角色的访问控制（RBAC）
5. **日志记录**：记录关键操作日志，便于审计和追踪
6. **输入验证**：前后端都进行输入验证，防止注入攻击
7. **会话管理**：使用HttpSession管理用户会话，设置合理的会话超时时间，提供安全的登出机制

## 7. 扩展性考虑

1. **模块化设计**：系统按功能模块划分，便于扩展新功能
2. **API版本控制**：API接口支持版本控制，便于后续升级
3. **配置化**：关键参数可配置，无需修改代码即可调整系统行为
4. **多端支持**：后端API设计支持多种客户端接入（Android、Web等）
5. **数据导出导入**：支持数据的批量导出和导入，便于数据迁移和备份
6. **消息推送机制**：设计支持多种消息推送方式，如APP内推送、短信、邮件等
7. **第三方集成**：预留与第三方系统集成的接口，如ERP、WMS等
