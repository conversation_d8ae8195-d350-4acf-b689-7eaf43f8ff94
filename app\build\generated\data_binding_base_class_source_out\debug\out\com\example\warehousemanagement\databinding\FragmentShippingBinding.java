// Generated by view binder compiler. Do not edit!
package com.example.warehousemanagement.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.warehousemanagement.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentShippingBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnSearch;

  @NonNull
  public final TextInputEditText etCustomerFilter;

  @NonNull
  public final TextInputEditText etEndDate;

  @NonNull
  public final TextInputEditText etRemarkFilter;

  @NonNull
  public final TextInputEditText etStartDate;

  @NonNull
  public final FloatingActionButton fabAddShipping;

  @NonNull
  public final RecyclerView rvShippingList;

  @NonNull
  public final Spinner spinnerItemType;

  private FragmentShippingBinding(@NonNull LinearLayout rootView, @NonNull MaterialButton btnSearch,
      @NonNull TextInputEditText etCustomerFilter, @NonNull TextInputEditText etEndDate,
      @NonNull TextInputEditText etRemarkFilter, @NonNull TextInputEditText etStartDate,
      @NonNull FloatingActionButton fabAddShipping, @NonNull RecyclerView rvShippingList,
      @NonNull Spinner spinnerItemType) {
    this.rootView = rootView;
    this.btnSearch = btnSearch;
    this.etCustomerFilter = etCustomerFilter;
    this.etEndDate = etEndDate;
    this.etRemarkFilter = etRemarkFilter;
    this.etStartDate = etStartDate;
    this.fabAddShipping = fabAddShipping;
    this.rvShippingList = rvShippingList;
    this.spinnerItemType = spinnerItemType;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentShippingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentShippingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_shipping, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentShippingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_search;
      MaterialButton btnSearch = ViewBindings.findChildViewById(rootView, id);
      if (btnSearch == null) {
        break missingId;
      }

      id = R.id.et_customer_filter;
      TextInputEditText etCustomerFilter = ViewBindings.findChildViewById(rootView, id);
      if (etCustomerFilter == null) {
        break missingId;
      }

      id = R.id.et_end_date;
      TextInputEditText etEndDate = ViewBindings.findChildViewById(rootView, id);
      if (etEndDate == null) {
        break missingId;
      }

      id = R.id.et_remark_filter;
      TextInputEditText etRemarkFilter = ViewBindings.findChildViewById(rootView, id);
      if (etRemarkFilter == null) {
        break missingId;
      }

      id = R.id.et_start_date;
      TextInputEditText etStartDate = ViewBindings.findChildViewById(rootView, id);
      if (etStartDate == null) {
        break missingId;
      }

      id = R.id.fab_add_shipping;
      FloatingActionButton fabAddShipping = ViewBindings.findChildViewById(rootView, id);
      if (fabAddShipping == null) {
        break missingId;
      }

      id = R.id.rv_shipping_list;
      RecyclerView rvShippingList = ViewBindings.findChildViewById(rootView, id);
      if (rvShippingList == null) {
        break missingId;
      }

      id = R.id.spinner_item_type;
      Spinner spinnerItemType = ViewBindings.findChildViewById(rootView, id);
      if (spinnerItemType == null) {
        break missingId;
      }

      return new FragmentShippingBinding((LinearLayout) rootView, btnSearch, etCustomerFilter,
          etEndDate, etRemarkFilter, etStartDate, fabAddShipping, rvShippingList, spinnerItemType);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
