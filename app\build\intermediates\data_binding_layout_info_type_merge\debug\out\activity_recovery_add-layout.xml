<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_recovery_add" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\activity_recovery_add.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_recovery_add_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="214" endOffset="14"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="9" startOffset="4" endLine="16" endOffset="54"/></Target><Target id="@+id/tv_connection_status" view="TextView"><Expressions/><location startLine="58" startOffset="24" endLine="62" endOffset="62"/></Target><Target id="@+id/btn_scan" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="67" startOffset="20" endLine="72" endOffset="49"/></Target><Target id="@+id/rv_scanned_tags" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="94" startOffset="20" endLine="98" endOffset="51"/></Target><Target id="@+id/tv_empty_tags" view="TextView"><Expressions/><location startLine="101" startOffset="20" endLine="108" endOffset="54"/></Target><Target id="@+id/et_customer_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="134" startOffset="24" endLine="137" endOffset="66"/></Target><Target id="@+id/et_recovery_date" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="146" startOffset="24" endLine="151" endOffset="54"/></Target><Target id="@+id/et_remark1" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="160" startOffset="24" endLine="163" endOffset="66"/></Target><Target id="@+id/et_remark2" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="173" startOffset="24" endLine="176" endOffset="66"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="196" startOffset="8" endLine="202" endOffset="44"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="204" startOffset="8" endLine="210" endOffset="46"/></Target></Targets></Layout>