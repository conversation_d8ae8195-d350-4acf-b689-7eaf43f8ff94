package com.example.warehousemanagement.model;

import java.util.List;

/**
 * 发货请求模型
 */
public class ShippingRequest {
    private String customerName;
    private String shippingDate;
    private List<ShippingTag> tags;
    
    public ShippingRequest(String customerName, String shippingDate, List<ShippingTag> tags) {
        this.customerName = customerName;
        this.shippingDate = shippingDate;
        this.tags = tags;
    }
    
    public String getCustomerName() {
        return customerName;
    }
    
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
    
    public String getShippingDate() {
        return shippingDate;
    }
    
    public void setShippingDate(String shippingDate) {
        this.shippingDate = shippingDate;
    }
    
    public List<ShippingTag> getTags() {
        return tags;
    }
    
    public void setTags(List<ShippingTag> tags) {
        this.tags = tags;
    }
    
    /**
     * 发货标签
     */
    public static class ShippingTag {
        private String tagId;
        private String remark1;
        private String remark2;
        
        public ShippingTag(String tagId) {
            this.tagId = tagId;
        }
        
        public String getTagId() {
            return tagId;
        }
        
        public void setTagId(String tagId) {
            this.tagId = tagId;
        }
        
        public String getRemark1() {
            return remark1;
        }
        
        public void setRemark1(String remark1) {
            this.remark1 = remark1;
        }
        
        public String getRemark2() {
            return remark2;
        }
        
        public void setRemark2(String remark2) {
            this.remark2 = remark2;
        }
    }
}
