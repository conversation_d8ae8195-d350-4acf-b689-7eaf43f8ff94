{"logs": [{"outputFile": "com.example.warehousemanagement.app-mergeDebugResources-62:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\60497f4189655a1239df256971621998\\transformed\\foundation-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,94", "endOffsets": "139,234"}, "to": {"startLines": "183,184", "startColumns": "4,4", "startOffsets": "17414,17503", "endColumns": "88,94", "endOffsets": "17498,17593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\24ccdfb3f311aaa88a834c682a4cfd8c\\transformed\\appcompat-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,16881", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,16963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\751a317a1d40c660b2a58fb1ab3f0187\\transformed\\material-1.11.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1110,1207,1290,1356,1458,1523,1598,1654,1733,1793,1847,1969,2028,2090,2144,2226,2361,2453,2537,2681,2760,2841,2982,3075,3154,3209,3260,3326,3406,3487,3590,3670,3743,3821,3894,3966,4078,4171,4243,4335,4427,4501,4585,4677,4734,4818,4884,4967,5054,5116,5180,5243,5321,5423,5527,5624,5728,5787,5842", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,140,92,78,54,50,65,79,80,102,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88", "endOffsets": "278,358,439,522,631,726,824,954,1039,1105,1202,1285,1351,1453,1518,1593,1649,1728,1788,1842,1964,2023,2085,2139,2221,2356,2448,2532,2676,2755,2836,2977,3070,3149,3204,3255,3321,3401,3482,3585,3665,3738,3816,3889,3961,4073,4166,4238,4330,4422,4496,4580,4672,4729,4813,4879,4962,5049,5111,5175,5238,5316,5418,5522,5619,5723,5782,5837,5926"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,58,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3155,3236,3319,3428,4250,4348,4478,5037,5103,5200,5454,11822,11924,11989,12064,12120,12199,12259,12313,12435,12494,12556,12610,12692,12827,12919,13003,13147,13226,13307,13448,13541,13620,13675,13726,13792,13872,13953,14056,14136,14209,14287,14360,14432,14544,14637,14709,14801,14893,14967,15051,15143,15200,15284,15350,15433,15520,15582,15646,15709,15787,15889,15993,16090,16194,16253,16626", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,58,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,174", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,140,92,78,54,50,65,79,80,102,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88", "endOffsets": "328,3150,3231,3314,3423,3518,4343,4473,4558,5098,5195,5278,5515,11919,11984,12059,12115,12194,12254,12308,12430,12489,12551,12605,12687,12822,12914,12998,13142,13221,13302,13443,13536,13615,13670,13721,13787,13867,13948,14051,14131,14204,14282,14355,14427,14539,14632,14704,14796,14888,14962,15046,15138,15195,15279,15345,15428,15515,15577,15641,15704,15782,15884,15988,16085,16189,16248,16303,16710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5426231ab31392e5b33d42e73e9b9039\\transformed\\material3-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,293,415,536,635,729,841,985,1104,1251,1335,1435,1536,1637,1758,1885,1990,2140,2286,2416,2608,2734,2852,2975,3108,3210,3315,3439,3564,3666,3773,3878,4023,4175,4284,4393,4480,4573,4668,4759,4845,4952,5032,5117,5219,5331,5429,5529,5617,5733,5834,5937,6069,6149,6259", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "170,288,410,531,630,724,836,980,1099,1246,1330,1430,1531,1632,1753,1880,1985,2135,2281,2411,2603,2729,2847,2970,3103,3205,3310,3434,3559,3661,3768,3873,4018,4170,4279,4388,4475,4568,4663,4754,4840,4947,5027,5112,5214,5326,5424,5524,5612,5728,5829,5932,6064,6144,6254,6352"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5520,5640,5758,5880,6001,6100,6194,6306,6450,6569,6716,6800,6900,7001,7102,7223,7350,7455,7605,7751,7881,8073,8199,8317,8440,8573,8675,8780,8904,9029,9131,9238,9343,9488,9640,9749,9858,9945,10038,10133,10224,10310,10417,10497,10582,10684,10796,10894,10994,11082,11198,11299,11402,11534,11614,11724", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "5635,5753,5875,5996,6095,6189,6301,6445,6564,6711,6795,6895,6996,7097,7218,7345,7450,7600,7746,7876,8068,8194,8312,8435,8568,8670,8775,8899,9024,9126,9233,9338,9483,9635,9744,9853,9940,10033,10128,10219,10305,10412,10492,10577,10679,10791,10889,10989,11077,11193,11294,11397,11529,11609,11719,11817"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c34cc1cda6217c045c9702ce14b9b574\\transformed\\core-1.16.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3523,3621,3723,3822,3924,4028,4132,17044", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3616,3718,3817,3919,4023,4127,4245,17140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78b878a2477ffed38acd39b519b92118\\transformed\\ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1000,1068,1149,1234,1310,1388,1457", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,995,1063,1144,1229,1305,1383,1452,1574"}, "to": {"startLines": "48,49,50,51,52,56,57,170,171,172,173,175,176,178,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4563,4662,4750,4850,4950,5283,5362,16308,16400,16487,16558,16715,16796,16968,17145,17223,17292", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "4657,4745,4845,4945,5032,5357,5449,16395,16482,16553,16621,16791,16876,17039,17218,17287,17409"}}]}]}