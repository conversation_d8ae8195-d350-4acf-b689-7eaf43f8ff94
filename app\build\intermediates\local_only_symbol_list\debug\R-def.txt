R_DEF: Internal format may change without notice
local
color accent
color background
color background_light
color black
color button_disabled
color button_primary
color button_secondary
color card_background
color divider
color edit_text_background
color edit_text_border
color edit_text_border_focused
color error
color error_light
color info
color primary
color primary_dark
color primary_light
color success
color success_light
color surface
color tab_selected
color tab_unselected
color text_hint
color text_primary
color text_secondary
color text_white
color warning
color warning_light
color white
drawable circle_background
drawable ic_arrow_back
drawable ic_arrow_forward
drawable ic_delete
drawable ic_info
drawable ic_inventory
drawable ic_login
drawable ic_person
drawable ic_profile
drawable ic_recycling
drawable ic_settings
drawable ic_shipping
drawable ic_tag
drawable status_background
id bottom_navigation
id btn_cancel
id btn_connect
id btn_login
id btn_logout
id btn_save
id btn_save_settings
id btn_scan
id btn_search
id btn_write_tag
id et_customer_filter
id et_customer_name
id et_end_date
id et_password
id et_recovery_date
id et_remark1
id et_remark2
id et_remark_filter
id et_server_url
id et_shipping_date
id et_start_date
id et_storage_date
id et_sync_interval
id et_timeout
id et_username
id et_write_content
id fab_add_recovery
id fab_add_shipping
id fab_add_storage
id fragment_container
id iv_avatar
id iv_delete
id iv_logo
id ll_about
id ll_customer
id ll_remark1
id ll_remark2
id ll_settings
id nav_profile
id nav_recovery
id nav_shipping
id nav_storage
id progress_bar
id rv_recovery_list
id rv_scanned_tags
id rv_shipping_list
id rv_storage_list
id slider_scan_power
id spinner_item_type
id swipe_refresh_layout
id switch_auto_connect
id switch_auto_sync
id tab_layout
id toolbar
id tv_connection_status
id tv_customer
id tv_date
id tv_empty_tags
id tv_error
id tv_error_details
id tv_record_id
id tv_remark1
id tv_remark2
id tv_rfid_status
id tv_role
id tv_scan_results
id tv_scan_time
id tv_status
id tv_tag_count
id tv_tag_id
id tv_username
id view_pager
layout activity_login
layout activity_main
layout activity_recovery_add
layout activity_settings
layout activity_shipping_add
layout activity_simple_rfid
layout activity_storage_add
layout fragment_error_details
layout fragment_profile
layout fragment_recovery
layout fragment_scan_results
layout fragment_shipping
layout fragment_storage
layout item_record
layout item_scanned_tag
menu bottom_navigation_menu
mipmap ic_launcher
mipmap ic_launcher_round
string about
string all_types
string app_name
string auto_connect
string auto_sync
string bag_expiry_days
string box_expiry_days
string cancel
string confirm
string connection_status
string connection_timeout
string customer_name
string customer_name_hint
string data_settings
string delete
string disconnected
string edit
string error_light
string expiry_days
string filter
string item_type
string item_type_bag
string item_type_box
string loading
string login_button
string login_error
string login_success
string login_title
string logout
string need_recovery
string network_error
string no
string no_data
string no_scanned_tags
string notification_enabled
string operation_failed
string operation_success
string password_hint
string profile
string profile_title
string recovery
string recovery_add
string recovery_date
string recovery_days
string recovery_info
string recovery_title
string refresh
string remaining_days
string remark1
string remark2
string rfid_connected
string rfid_connecting
string rfid_disconnected
string rfid_scan_area
string rfid_scan_start
string rfid_scan_stop
string rfid_settings
string save
string scan_distance
string scan_power
string scan_tags
string scanned_tags
string search
string server_settings
string server_url
string settings
string shipping
string shipping_add
string shipping_date
string shipping_title
string start_scan
string storage
string storage_add
string storage_date
string storage_remark
string storage_title
string sync_interval
string system_settings
string tab_profile
string tab_recovery
string tab_shipping
string tab_storage
string tag_already_exists
string tag_id
string tag_not_found
string used_days
string username_hint
string warning_light
string yes
style Base.Theme.WarehouseManagement
style BodyText
style BodyTextStyle
style CardStyle
style EditTextStyle
style LoginTheme
style PrimaryButton
style SecondaryButton
style SubtitleText
style SubtitleTextStyle
style Theme.WarehouseManagement
style TitleText
style TitleTextStyle
