<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background">

    <!-- 顶部工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="@string/system_settings"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 服务器设置 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/CardStyle"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        style="@style/SubtitleText"
                        android:text="@string/server_settings"
                        android:layout_marginBottom="16dp" />

                    <!-- 服务器地址 -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/EditTextStyle"
                        android:hint="@string/server_url">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_server_url"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textUri" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 连接超时 -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/EditTextStyle"
                        android:hint="@string/connection_timeout"
                        android:layout_marginBottom="0dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_timeout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- RFID设置 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/CardStyle"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        style="@style/SubtitleText"
                        android:text="@string/rfid_settings"
                        android:layout_marginBottom="16dp" />

                    <!-- 自动连接 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            style="@style/BodyText"
                            android:text="@string/auto_connect"
                            android:layout_weight="1" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switch_auto_connect"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                    <!-- 扫描功率 -->
                    <TextView
                        style="@style/BodyText"
                        android:text="@string/scan_power"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.slider.Slider
                        android:id="@+id/slider_scan_power"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:valueFrom="1"
                        android:valueTo="10"
                        android:stepSize="1"
                        android:value="5" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 数据设置 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/CardStyle">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        style="@style/SubtitleText"
                        android:text="@string/data_settings"
                        android:layout_marginBottom="16dp" />

                    <!-- 自动同步 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            style="@style/BodyText"
                            android:text="@string/auto_sync"
                            android:layout_weight="1" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switch_auto_sync"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                    <!-- 同步间隔 -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/EditTextStyle"
                        android:hint="@string/sync_interval"
                        android:layout_marginBottom="0dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_sync_interval"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

    <!-- 底部保存按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_save_settings"
        style="@style/PrimaryButton"
        android:text="@string/save"
        android:layout_margin="16dp" />

</LinearLayout>