// Generated by view binder compiler. Do not edit!
package com.example.warehousemanagement.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.warehousemanagement.R;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityShippingAddBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnCancel;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final MaterialButton btnScan;

  @NonNull
  public final TextInputEditText etCustomerName;

  @NonNull
  public final TextInputEditText etRemark1;

  @NonNull
  public final TextInputEditText etRemark2;

  @NonNull
  public final TextInputEditText etShippingDate;

  @NonNull
  public final RecyclerView rvScannedTags;

  @NonNull
  public final MaterialToolbar toolbar;

  @NonNull
  public final TextView tvEmptyTags;

  @NonNull
  public final TextView tvRfidStatus;

  @NonNull
  public final TextView tvTagCount;

  private ActivityShippingAddBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton btnCancel, @NonNull MaterialButton btnSave,
      @NonNull MaterialButton btnScan, @NonNull TextInputEditText etCustomerName,
      @NonNull TextInputEditText etRemark1, @NonNull TextInputEditText etRemark2,
      @NonNull TextInputEditText etShippingDate, @NonNull RecyclerView rvScannedTags,
      @NonNull MaterialToolbar toolbar, @NonNull TextView tvEmptyTags,
      @NonNull TextView tvRfidStatus, @NonNull TextView tvTagCount) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnSave = btnSave;
    this.btnScan = btnScan;
    this.etCustomerName = etCustomerName;
    this.etRemark1 = etRemark1;
    this.etRemark2 = etRemark2;
    this.etShippingDate = etShippingDate;
    this.rvScannedTags = rvScannedTags;
    this.toolbar = toolbar;
    this.tvEmptyTags = tvEmptyTags;
    this.tvRfidStatus = tvRfidStatus;
    this.tvTagCount = tvTagCount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityShippingAddBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityShippingAddBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_shipping_add, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityShippingAddBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel;
      MaterialButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_save;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.btn_scan;
      MaterialButton btnScan = ViewBindings.findChildViewById(rootView, id);
      if (btnScan == null) {
        break missingId;
      }

      id = R.id.et_customer_name;
      TextInputEditText etCustomerName = ViewBindings.findChildViewById(rootView, id);
      if (etCustomerName == null) {
        break missingId;
      }

      id = R.id.et_remark1;
      TextInputEditText etRemark1 = ViewBindings.findChildViewById(rootView, id);
      if (etRemark1 == null) {
        break missingId;
      }

      id = R.id.et_remark2;
      TextInputEditText etRemark2 = ViewBindings.findChildViewById(rootView, id);
      if (etRemark2 == null) {
        break missingId;
      }

      id = R.id.et_shipping_date;
      TextInputEditText etShippingDate = ViewBindings.findChildViewById(rootView, id);
      if (etShippingDate == null) {
        break missingId;
      }

      id = R.id.rv_scanned_tags;
      RecyclerView rvScannedTags = ViewBindings.findChildViewById(rootView, id);
      if (rvScannedTags == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_empty_tags;
      TextView tvEmptyTags = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyTags == null) {
        break missingId;
      }

      id = R.id.tv_rfid_status;
      TextView tvRfidStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvRfidStatus == null) {
        break missingId;
      }

      id = R.id.tv_tag_count;
      TextView tvTagCount = ViewBindings.findChildViewById(rootView, id);
      if (tvTagCount == null) {
        break missingId;
      }

      return new ActivityShippingAddBinding((LinearLayout) rootView, btnCancel, btnSave, btnScan,
          etCustomerName, etRemark1, etRemark2, etShippingDate, rvScannedTags, toolbar, tvEmptyTags,
          tvRfidStatus, tvTagCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
