package com.example.warehousemanagement.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.warehousemanagement.databinding.ItemRecordBinding;
import com.example.warehousemanagement.model.RfidTag;
import com.example.warehousemanagement.utils.DateUtils;
import java.util.List;

/**
 * 记录列表适配器
 * 用于显示入库、发货、回收记录
 */
public class RecordAdapter extends RecyclerView.Adapter<RecordAdapter.RecordViewHolder> {

    private List<RfidTag> recordList;
    private OnItemClickListener onItemClickListener;

    public interface OnItemClickListener {
        void onItemClick(RfidTag record);
        void onItemLongClick(RfidTag record);
    }

    public RecordAdapter(List<RfidTag> recordList) {
        this.recordList = recordList;
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    @NonNull
    @Override
    public RecordViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemRecordBinding binding = ItemRecordBinding.inflate(
            LayoutInflater.from(parent.getContext()), parent, false);
        return new RecordViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull RecordViewHolder holder, int position) {
        RfidTag record = recordList.get(position);
        holder.bind(record);
    }

    @Override
    public int getItemCount() {
        return recordList.size();
    }

    class RecordViewHolder extends RecyclerView.ViewHolder {
        private ItemRecordBinding binding;

        public RecordViewHolder(ItemRecordBinding binding) {
            super(binding.getRoot());
            this.binding = binding;

            // 设置点击事件
            binding.getRoot().setOnClickListener(v -> {
                if (onItemClickListener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        onItemClickListener.onItemClick(recordList.get(position));
                    }
                }
            });

            binding.getRoot().setOnLongClickListener(v -> {
                if (onItemClickListener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        onItemClickListener.onItemLongClick(recordList.get(position));
                        return true;
                    }
                }
                return false;
            });
        }

        public void bind(RfidTag record) {
            // 显示记录ID
            binding.tvRecordId.setText(String.valueOf(record.getId()));

            // 显示状态
            binding.tvStatus.setText(record.getTagStatus() != null ? record.getTagStatus() : "");

            // 显示日期信息
            StringBuilder dateInfo = new StringBuilder();
            if (record.getStorageDate() != null) {
                dateInfo.append("入库: ").append(DateUtils.formatDate(record.getStorageDate()));
            }
            if (record.getShippingDate() != null) {
                if (dateInfo.length() > 0) dateInfo.append("\n");
                dateInfo.append("发货: ").append(DateUtils.formatDate(record.getShippingDate()));
            }
            if (record.getRecoveryDate() != null) {
                if (dateInfo.length() > 0) dateInfo.append("\n");
                dateInfo.append("回收: ").append(DateUtils.formatDate(record.getRecoveryDate()));
            }
            binding.tvDate.setText(dateInfo.toString());

            // 显示客户名称
            if (record.getCustomerName() != null && !record.getCustomerName().isEmpty()) {
                binding.tvCustomer.setText(record.getCustomerName());
                binding.llCustomer.setVisibility(View.VISIBLE);
            } else {
                binding.llCustomer.setVisibility(View.GONE);
            }

            // 显示备注1
            if (record.getRemark1() != null && !record.getRemark1().isEmpty()) {
                binding.tvRemark1.setText(record.getRemark1());
                binding.llRemark1.setVisibility(View.VISIBLE);
            } else {
                binding.llRemark1.setVisibility(View.GONE);
            }

            // 显示备注2
            if (record.getRemark2() != null && !record.getRemark2().isEmpty()) {
                binding.tvRemark2.setText(record.getRemark2());
                binding.llRemark2.setVisibility(View.VISIBLE);
            } else {
                binding.llRemark2.setVisibility(View.GONE);
            }

            // 显示标签数量（可以根据需要设置）
            binding.tvTagCount.setText("1");
        }
    }
}
