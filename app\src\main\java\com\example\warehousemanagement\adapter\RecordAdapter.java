package com.example.warehousemanagement.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.warehousemanagement.databinding.ItemRecordBinding;
import com.example.warehousemanagement.model.RfidTag;
import com.example.warehousemanagement.utils.DateUtils;
import java.util.List;

/**
 * 记录列表适配器
 * 用于显示入库、发货、回收记录
 */
public class RecordAdapter extends RecyclerView.Adapter<RecordAdapter.RecordViewHolder> {
    
    private List<RfidTag> recordList;
    private OnItemClickListener onItemClickListener;
    
    public interface OnItemClickListener {
        void onItemClick(RfidTag record);
        void onItemLongClick(RfidTag record);
    }
    
    public RecordAdapter(List<RfidTag> recordList) {
        this.recordList = recordList;
    }
    
    public void setOnItemClickListener(OnItemClickListener listener) {
        this.onItemClickListener = listener;
    }
    
    @NonNull
    @Override
    public RecordViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemRecordBinding binding = ItemRecordBinding.inflate(
            LayoutInflater.from(parent.getContext()), parent, false);
        return new RecordViewHolder(binding);
    }
    
    @Override
    public void onBindViewHolder(@NonNull RecordViewHolder holder, int position) {
        RfidTag record = recordList.get(position);
        holder.bind(record);
    }
    
    @Override
    public int getItemCount() {
        return recordList.size();
    }
    
    class RecordViewHolder extends RecyclerView.ViewHolder {
        private ItemRecordBinding binding;
        
        public RecordViewHolder(ItemRecordBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
            
            // 设置点击事件
            binding.getRoot().setOnClickListener(v -> {
                if (onItemClickListener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        onItemClickListener.onItemClick(recordList.get(position));
                    }
                }
            });
            
            binding.getRoot().setOnLongClickListener(v -> {
                if (onItemClickListener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        onItemClickListener.onItemLongClick(recordList.get(position));
                        return true;
                    }
                }
                return false;
            });
        }
        
        public void bind(RfidTag record) {
            // 显示标签ID
            binding.tvTagId.setText(record.getTagId());
            
            // 显示标签类型
            String tagType = "未知";
            if ("x".equals(record.getTagType())) {
                tagType = "箱子";
            } else if ("d".equals(record.getTagType())) {
                tagType = "袋子";
            }
            binding.tvTagType.setText(tagType);
            
            // 显示状态
            binding.tvStatus.setText(record.getTagStatus() != null ? record.getTagStatus() : "");
            
            // 显示日期信息
            StringBuilder dateInfo = new StringBuilder();
            if (record.getStorageDate() != null) {
                dateInfo.append("入库: ").append(DateUtils.formatDate(record.getStorageDate()));
            }
            if (record.getShippingDate() != null) {
                if (dateInfo.length() > 0) dateInfo.append("\n");
                dateInfo.append("发货: ").append(DateUtils.formatDate(record.getShippingDate()));
            }
            if (record.getRecoveryDate() != null) {
                if (dateInfo.length() > 0) dateInfo.append("\n");
                dateInfo.append("回收: ").append(DateUtils.formatDate(record.getRecoveryDate()));
            }
            binding.tvDate.setText(dateInfo.toString());
            
            // 显示客户名称
            if (record.getCustomerName() != null && !record.getCustomerName().isEmpty()) {
                binding.tvCustomer.setText("客户: " + record.getCustomerName());
                binding.tvCustomer.setVisibility(View.VISIBLE);
            } else {
                binding.tvCustomer.setVisibility(View.GONE);
            }
            
            // 显示备注
            StringBuilder remarkInfo = new StringBuilder();
            if (record.getRemark1() != null && !record.getRemark1().isEmpty()) {
                remarkInfo.append(record.getRemark1());
            }
            if (record.getRemark2() != null && !record.getRemark2().isEmpty()) {
                if (remarkInfo.length() > 0) remarkInfo.append(" | ");
                remarkInfo.append(record.getRemark2());
            }
            
            if (remarkInfo.length() > 0) {
                binding.tvRemark.setText(remarkInfo.toString());
                binding.tvRemark.setVisibility(View.VISIBLE);
            } else {
                binding.tvRemark.setVisibility(View.GONE);
            }
            
            // 显示保质期信息（仅回收记录显示）
            if (record.getExpiryDays() > 0) {
                StringBuilder expiryInfo = new StringBuilder();
                expiryInfo.append("保质期: ").append(record.getExpiryDays()).append("天");
                expiryInfo.append(" | 已用: ").append(record.getUsedDays()).append("天");
                expiryInfo.append(" | 剩余: ").append(record.getRemainingDays()).append("天");
                
                binding.tvExpiryInfo.setText(expiryInfo.toString());
                binding.tvExpiryInfo.setVisibility(View.VISIBLE);
                
                // 根据是否需要回收设置颜色
                if (record.isNeedRecovery()) {
                    binding.tvExpiryInfo.setTextColor(
                        binding.getRoot().getContext().getColor(android.R.color.holo_red_dark));
                } else {
                    binding.tvExpiryInfo.setTextColor(
                        binding.getRoot().getContext().getColor(android.R.color.darker_gray));
                }
            } else {
                binding.tvExpiryInfo.setVisibility(View.GONE);
            }
        }
    }
}
