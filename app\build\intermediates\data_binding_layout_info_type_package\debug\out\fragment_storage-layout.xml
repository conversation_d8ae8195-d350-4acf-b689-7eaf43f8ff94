<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_storage" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\fragment_storage.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_storage_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="124" endOffset="14"/></Target><Target id="@+id/et_start_date" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="34" startOffset="20" endLine="39" endOffset="50"/></Target><Target id="@+id/et_end_date" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="51" startOffset="20" endLine="56" endOffset="50"/></Target><Target id="@+id/et_remark_filter" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="77" startOffset="20" endLine="80" endOffset="62"/></Target><Target id="@+id/spinner_item_type" view="Spinner"><Expressions/><location startLine="84" startOffset="16" endLine="89" endOffset="54"/></Target><Target id="@+id/btn_search" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="94" startOffset="12" endLine="98" endOffset="46"/></Target><Target id="@+id/rv_storage_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="105" startOffset="4" endLine="111" endOffset="44"/></Target><Target id="@+id/fab_add_storage" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="114" startOffset="4" endLine="122" endOffset="45"/></Target></Targets></Layout>