package com.example.warehousemanagement.network;

import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import java.util.concurrent.TimeUnit;

/**
 * API客户端
 * 管理Retrofit实例和网络配置
 */
public class ApiClient {
    
    private static final String BASE_URL = "http://127.0.0.1/";
    private static final int CONNECT_TIMEOUT = 30;
    private static final int READ_TIMEOUT = 30;
    private static final int WRITE_TIMEOUT = 30;
    
    private static ApiClient instance;
    private Retrofit retrofit;
    private ApiService apiService;
    
    private ApiClient() {
        initRetrofit();
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized ApiClient getInstance() {
        if (instance == null) {
            instance = new ApiClient();
        }
        return instance;
    }
    
    /**
     * 初始化Retrofit
     */
    private void initRetrofit() {
        // 创建日志拦截器
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor();
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        
        // 创建OkHttpClient
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                .addInterceptor(loggingInterceptor)
                .addInterceptor(new SessionInterceptor())
                .build();
        
        // 创建Retrofit实例
        retrofit = new Retrofit.Builder()
                .baseUrl(BASE_URL)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build();
        
        // 创建API服务
        apiService = retrofit.create(ApiService.class);
    }
    
    /**
     * 获取API服务
     */
    public ApiService getApiService() {
        return apiService;
    }
    
    /**
     * 获取Retrofit实例
     */
    public Retrofit getRetrofit() {
        return retrofit;
    }
}
