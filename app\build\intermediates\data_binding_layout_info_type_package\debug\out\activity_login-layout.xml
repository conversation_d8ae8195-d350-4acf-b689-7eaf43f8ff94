<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_login_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="100" endOffset="14"/></Target><Target id="@+id/iv_logo" view="ImageView"><Expressions/><location startLine="11" startOffset="4" endLine="17" endOffset="55"/></Target><Target id="@+id/et_username" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="47" startOffset="16" endLine="52" endOffset="42"/></Target><Target id="@+id/et_password" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="62" startOffset="16" endLine="67" endOffset="42"/></Target><Target id="@+id/btn_login" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="72" startOffset="12" endLine="76" endOffset="49"/></Target><Target id="@+id/tv_error" view="TextView"><Expressions/><location startLine="83" startOffset="4" endLine="90" endOffset="41"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="93" startOffset="4" endLine="98" endOffset="35"/></Target></Targets></Layout>