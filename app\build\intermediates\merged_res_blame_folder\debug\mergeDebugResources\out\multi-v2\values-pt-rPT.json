{"logs": [{"outputFile": "com.example.warehousemanagement.app-mergeDebugResources-62:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c34cc1cda6217c045c9702ce14b9b574\\transformed\\core-1.16.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3492,3589,3691,3790,3890,3997,4103,16960", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3584,3686,3785,3885,3992,4098,4219,17056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\60497f4189655a1239df256971621998\\transformed\\foundation-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,88", "endOffsets": "137,226"}, "to": {"startLines": "183,184", "startColumns": "4,4", "startOffsets": "17331,17418", "endColumns": "86,88", "endOffsets": "17413,17502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5426231ab31392e5b33d42e73e9b9039\\transformed\\material3-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,296,419,539,641,740,856,997,1115,1260,1344,1446,1544,1644,1759,1886,1993,2138,2282,2428,2620,2758,2879,3003,3129,3228,3325,3450,3588,3692,3805,3910,4056,4207,4317,4422,4508,4603,4698,4788,4875,4976,5056,5140,5241,5346,5439,5539,5627,5737,5838,5943,6062,6142,6246", "endColumns": "120,119,122,119,101,98,115,140,117,144,83,101,97,99,114,126,106,144,143,145,191,137,120,123,125,98,96,124,137,103,112,104,145,150,109,104,85,94,94,89,86,100,79,83,100,104,92,99,87,109,100,104,118,79,103,95", "endOffsets": "171,291,414,534,636,735,851,992,1110,1255,1339,1441,1539,1639,1754,1881,1988,2133,2277,2423,2615,2753,2874,2998,3124,3223,3320,3445,3583,3687,3800,3905,4051,4202,4312,4417,4503,4598,4693,4783,4870,4971,5051,5135,5236,5341,5434,5534,5622,5732,5833,5938,6057,6137,6241,6337"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5464,5585,5705,5828,5948,6050,6149,6265,6406,6524,6669,6753,6855,6953,7053,7168,7295,7402,7547,7691,7837,8029,8167,8288,8412,8538,8637,8734,8859,8997,9101,9214,9319,9465,9616,9726,9831,9917,10012,10107,10197,10284,10385,10465,10549,10650,10755,10848,10948,11036,11146,11247,11352,11471,11551,11655", "endColumns": "120,119,122,119,101,98,115,140,117,144,83,101,97,99,114,126,106,144,143,145,191,137,120,123,125,98,96,124,137,103,112,104,145,150,109,104,85,94,94,89,86,100,79,83,100,104,92,99,87,109,100,104,118,79,103,95", "endOffsets": "5580,5700,5823,5943,6045,6144,6260,6401,6519,6664,6748,6850,6948,7048,7163,7290,7397,7542,7686,7832,8024,8162,8283,8407,8533,8632,8729,8854,8992,9096,9209,9314,9460,9611,9721,9826,9912,10007,10102,10192,10279,10380,10460,10544,10645,10750,10843,10943,11031,11141,11242,11347,11466,11546,11650,11746"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\751a317a1d40c660b2a58fb1ab3f0187\\transformed\\material-1.11.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1079,1171,1250,1315,1405,1469,1537,1599,1672,1736,1790,1916,1974,2036,2090,2166,2309,2396,2478,2617,2699,2781,2917,3004,3084,3140,3191,3257,3332,3412,3499,3578,3651,3728,3801,3875,3982,4075,4152,4245,4343,4417,4498,4597,4650,4734,4800,4889,4977,5039,5103,5166,5234,5350,5458,5565,5667,5727,5782", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,81,98,95,102,119,80,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,81,138,81,81,135,86,79,55,50,65,74,79,86,78,72,76,72,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85", "endOffsets": "268,349,429,511,610,706,809,929,1010,1074,1166,1245,1310,1400,1464,1532,1594,1667,1731,1785,1911,1969,2031,2085,2161,2304,2391,2473,2612,2694,2776,2912,2999,3079,3135,3186,3252,3327,3407,3494,3573,3646,3723,3796,3870,3977,4070,4147,4240,4338,4412,4493,4592,4645,4729,4795,4884,4972,5034,5098,5161,5229,5345,5453,5560,5662,5722,5777,5863"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,58,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3054,3135,3215,3297,3396,4224,4327,4447,4988,5052,5144,5399,11751,11841,11905,11973,12035,12108,12172,12226,12352,12410,12472,12526,12602,12745,12832,12914,13053,13135,13217,13353,13440,13520,13576,13627,13693,13768,13848,13935,14014,14087,14164,14237,14311,14418,14511,14588,14681,14779,14853,14934,15033,15086,15170,15236,15325,15413,15475,15539,15602,15670,15786,15894,16001,16103,16163,16537", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,58,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,174", "endColumns": "12,80,79,81,98,95,102,119,80,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,81,138,81,81,135,86,79,55,50,65,74,79,86,78,72,76,72,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85", "endOffsets": "318,3130,3210,3292,3391,3487,4322,4442,4523,5047,5139,5218,5459,11836,11900,11968,12030,12103,12167,12221,12347,12405,12467,12521,12597,12740,12827,12909,13048,13130,13212,13348,13435,13515,13571,13622,13688,13763,13843,13930,14009,14082,14159,14232,14306,14413,14506,14583,14676,14774,14848,14929,15028,15081,15165,15231,15320,15408,15470,15534,15597,15665,15781,15889,15996,16098,16158,16213,16618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78b878a2477ffed38acd39b519b92118\\transformed\\ui-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,380,479,565,644,741,832,919,991,1060,1145,1235,1311,1387,1459", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "195,278,375,474,560,639,736,827,914,986,1055,1140,1230,1306,1382,1454,1576"}, "to": {"startLines": "48,49,50,51,52,56,57,170,171,172,173,175,176,178,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4528,4623,4706,4803,4902,5223,5302,16218,16309,16396,16468,16623,16708,16884,17061,17137,17209", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "4618,4701,4798,4897,4983,5297,5394,16304,16391,16463,16532,16703,16793,16955,17132,17204,17326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\24ccdfb3f311aaa88a834c682a4cfd8c\\transformed\\appcompat-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,537,644,733,834,952,1037,1117,1209,1303,1400,1494,1593,1687,1783,1878,1970,2062,2147,2254,2365,2467,2575,2683,2790,2955,16798", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "426,532,639,728,829,947,1032,1112,1204,1298,1395,1489,1588,1682,1778,1873,1965,2057,2142,2249,2360,2462,2570,2678,2785,2950,3049,16879"}}]}]}