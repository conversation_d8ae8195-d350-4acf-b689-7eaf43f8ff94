{"logs": [{"outputFile": "com.example.warehousemanagement.app-mergeDebugResources-37:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ff72482a731973c8e0ce199a0d194ac5\\transformed\\appcompat-1.6.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "432,552,655,771,857,962,1081,1161,1238,1330,1424,1519,1613,1708,1802,1898,1993,2085,2177,2258,2364,2469,2567,2675,2781,2889,3062,8934", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "547,650,766,852,957,1076,1156,1233,1325,1419,1514,1608,1703,1797,1893,1988,2080,2172,2253,2359,2464,2562,2670,2776,2884,3057,3157,9011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e8cdd8f14c70e551ad9fcc8ba2fb4e74\\transformed\\material-1.9.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,382,459,536,618,715,807,904,1036,1119,1186,1279,1356,1419,1535,1598,1667,1726,1797,1856,1910,2031,2092,2155,2209,2282,2404,2492,2575,2727,2813,2900,2991,3048,3099,3165,3237,3314,3398,3473,3550,3632,3708,3797,3879,3970,4066,4140,4221,4316,4370,4436,4523,4609,4671,4735,4798,4908,5015,5118,5227,5288,5343", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,90,56,50,65,71,76,83,74,76,81,75,88,81,90,95,73,80,94,53,65,86,85,61,63,62,109,106,102,108,60,54,79", "endOffsets": "377,454,531,613,710,802,899,1031,1114,1181,1274,1351,1414,1530,1593,1662,1721,1792,1851,1905,2026,2087,2150,2204,2277,2399,2487,2570,2722,2808,2895,2986,3043,3094,3160,3232,3309,3393,3468,3545,3627,3703,3792,3874,3965,4061,4135,4216,4311,4365,4431,4518,4604,4666,4730,4793,4903,5010,5113,5222,5283,5338,5418"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3162,3239,3316,3398,3495,4318,4415,4547,4630,4697,4790,4867,4930,5046,5109,5178,5237,5308,5367,5421,5542,5603,5666,5720,5793,5915,6003,6086,6238,6324,6411,6502,6559,6610,6676,6748,6825,6909,6984,7061,7143,7219,7308,7390,7481,7577,7651,7732,7827,7881,7947,8034,8120,8182,8246,8309,8419,8526,8629,8738,8799,8854", "endLines": "7,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,90,56,50,65,71,76,83,74,76,81,75,88,81,90,95,73,80,94,53,65,86,85,61,63,62,109,106,102,108,60,54,79", "endOffsets": "427,3234,3311,3393,3490,3582,4410,4542,4625,4692,4785,4862,4925,5041,5104,5173,5232,5303,5362,5416,5537,5598,5661,5715,5788,5910,5998,6081,6233,6319,6406,6497,6554,6605,6671,6743,6820,6904,6979,7056,7138,7214,7303,7385,7476,7572,7646,7727,7822,7876,7942,8029,8115,8177,8241,8304,8414,8521,8624,8733,8794,8849,8929"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\03f312cdf143f005647525320abeeeeb\\transformed\\core-1.10.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "40,41,42,43,44,45,46,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3587,3685,3787,3887,3988,4094,4197,9016", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3680,3782,3882,3983,4089,4192,4313,9112"}}]}]}