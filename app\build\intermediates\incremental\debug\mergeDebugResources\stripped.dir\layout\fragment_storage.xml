<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background">

    <!-- 搜索和筛选区域 -->
    <com.google.android.material.card.MaterialCardView
        style="@style/CardStyle"
        android:layout_margin="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 日期筛选 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/EditTextStyle"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:layout_marginBottom="0dp"
                    android:hint="开始日期">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_start_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:focusable="false"
                        android:clickable="true" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/EditTextStyle"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:layout_marginBottom="0dp"
                    android:hint="结束日期">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_end_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:focusable="false"
                        android:clickable="true" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <!-- 备注和类型筛选 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/EditTextStyle"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:layout_marginBottom="0dp"
                    android:hint="@string/storage_remark">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_remark_filter"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </com.google.android.material.textfield.TextInputLayout>

                <Spinner
                    android:id="@+id/spinner_item_type"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="48dp"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <!-- 搜索按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_search"
                style="@style/SecondaryButton"
                android:text="@string/search"
                android:layout_height="40dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 记录列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_storage_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp" />

    <!-- 添加按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_storage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_margin="16dp"
        android:src="@android:drawable/ic_input_add"
        android:contentDescription="@string/storage_add"
        app:backgroundTint="@color/primary" />

</LinearLayout>