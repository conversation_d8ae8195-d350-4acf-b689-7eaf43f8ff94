package densowave.com.DensoScannerSDK_demo;

import android.os.Handler;
import android.util.Log;
import com.densowave.scannersdk.Common.CommScanner;
import com.densowave.scannersdk.RFID.RFIDScanner;
import com.densowave.scannersdk.RFID.*;
import com.densowave.scannersdk.RFID.RFIDException;
import com.densowave.scannersdk.Listener.RFIDDataDelegate;
import com.densowave.scannersdk.RFID.RFIDDataReceivedEvent;
import com.densowave.scannersdk.RFID.RFIDData;
import com.densowave.scannersdk.Dto.RFIDScannerSettings;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
// 导入RfidWriteActivity用于日志记录
import static densowave.com.DensoScannerSDK_demo.RfidWriteActivity.logMessage;

/**
 * RFID标签操作工具类（支持读取UII后写入）
 */
public class RFIDReadWriteHelper implements RFIDDataDelegate {
    private static final String TAG = "RFIDHelper";
    private static final byte[] DEFAULT_PASSWORD = {0, 0, 0, 0};
    private static final long DEFAULT_TIMEOUT = 10000;
    private static final short UII_EPC_START_ADDR = 4;
    private Handler handler = new Handler();
    
    private CommScanner commScanner;
    private RFIDScanner rfidScanner;
    private CountDownLatch inventoryLatch;
    private byte[] lastScannedUii;
    private boolean isScanning = false;

    public RFIDReadWriteHelper(CommScanner commScanner) {
        this.commScanner = commScanner;
        this.rfidScanner = commScanner.getRFIDScanner();
    }

    /**
     * 扫描并获取一个标签的UII（同步方法）
     * @param timeoutMillis 超时时间(毫秒)
     * @return 标签的UII字节数组，超时或失败返回null
     */
    public byte[] scanSingleTagUii(long timeoutMillis) {
        try {
            logMessage("开始扫描单个标签UII，超时时间: " + timeoutMillis + "ms");
            inventoryLatch = new CountDownLatch(1);
            lastScannedUii = null;
            isScanning = true;
            logMessage("已初始化扫描参数");
            
            // 配置扫描参数
            RFIDScannerSettings settings = rfidScanner.getSettings();
            settings.scan.triggerMode = RFIDScannerSettings.Scan.TriggerMode.AUTO_OFF;
            //rfidScanner.setSettings(settings);
            logMessage("已配置扫描参数，触发模式: AUTO_OFF");
            
            rfidScanner.setDataDelegate(this);
            logMessage("已设置数据委托");
            rfidScanner.openInventory();
            logMessage("已开始扫描");
            
            // 等待扫描结果
            logMessage("等待扫描结果...");
            if (inventoryLatch.await(timeoutMillis, TimeUnit.MILLISECONDS)) {
                if (lastScannedUii != null) {
                    StringBuilder uiiStr = new StringBuilder();
                    for (byte b : lastScannedUii) {
                        uiiStr.append(String.format("%02X ", b));
                    }
                    logMessage("扫描成功，获取到UII: " + uiiStr.toString().trim());
                    return lastScannedUii;
                } else {
                    logMessage("扫描完成但未获取到有效UII");
                }
            } else {
                logMessage("扫描超时，未获取到标签UII");
            }
            return null;
        } catch (Exception e) {
            Log.e(TAG, "扫描标签失败: " + e.getMessage());
            logMessage("扫描标签异常: " + e.getMessage());
            return null;
        } finally {
            stopScanning();
            logMessage("扫描已停止");
        }
    }

    /**
     * 写入数据到指定UII的标签
     * @param targetUii 目标标签UII
     * @param dataToWrite 要写入的数据字符串
     * @param bank 存储区（USER/UII/TID）
     * @param startAddr 起始地址（需为偶数）
     * @return 是否写入成功
     */
    public boolean writeToTag(byte[] targetUii, String dataToWrite, RFIDScannerSettings.RFIDBank bank, short startAddr) {
        if (targetUii == null || targetUii.length == 0) {
            Log.e(TAG, "无效的UII参数");
            logMessage("无效的UII参数");
            return false;
        }
        
        try {
            byte[] dataBytes = dataToWrite.getBytes();
            // 确保数据长度为偶数（WORD对齐）
            if (dataBytes.length % 2 != 0) {
                byte[] alignedData = new byte[dataBytes.length + 1];
                System.arraycopy(dataBytes, 0, alignedData, 0, dataBytes.length);
                dataBytes = alignedData;
            }
            logMessage("开始标签写入");
            rfidScanner.writeOneTag(
                bank,
                startAddr,
                (short) dataBytes.length,
                DEFAULT_PASSWORD,
                dataBytes,
                targetUii,
                DEFAULT_TIMEOUT
            );
            Log.i(TAG, "标签写入成功");
            logMessage("标签写入成功");
            return true;
        } catch (RFIDException e) {
            Log.e(TAG, "写入失败，错误码: " + e.getErrorCode());
            logMessage("写入失败，错误码: " + e.getErrorCode());
            return false;
        }
    }

    /**
     * 完整流程：扫描标签并写入数据（同步方法）
     * @param dataToWrite 要写入的数据
     * @param bank 存储区（默认USER Bank）
     * @param startAddr 起始地址（默认0）
     * @param scanTimeout 扫描超时时间(毫秒)
     * @return 是否成功完成整个流程
     */
    public boolean scanAndWrite(String dataToWrite, RFIDScannerSettings.RFIDBank bank, short startAddr, long scanTimeout) throws RFIDException {
        try {
            // 记录开始执行
            Log.i(TAG, "开始执行scanAndWrite方法");
            logMessage("开始执行scanAndWrite方法");
            
            // 确保之前的扫描已经关闭
            stopScanning();
            logMessage("已停止之前的扫描");
            
            // 等待一小段时间确保扫描器状态已重置
            try {
                logMessage("等待100ms确保扫描器状态重置");
                Thread.sleep(100);
                logMessage("等待完成");
            } catch (InterruptedException e) {
                logMessage("等待被中断: " + e.getMessage());
                // 忽略中断异常
            }
            
            // 配置扫描参数
            logMessage("开始配置扫描参数");
            RFIDScannerSettings settings = rfidScanner.getSettings();
            logMessage("已获取扫描器设置");
            settings.scan.triggerMode = RFIDScannerSettings.Scan.TriggerMode.AUTO_OFF; // 需按一次键触发
            logMessage("已设置触发模式为AUTO_OFF");
            //rfidScanner.setSettings(settings);
            
            // 扫描标签
            logMessage("开始扫描标签，超时时间: " + scanTimeout + "ms");
            byte[] uii = scanSingleTagUii(scanTimeout);
            if (uii == null) {
                Log.e(TAG, "未扫描到标签");
                logMessage("未扫描到标签，返回失败");
                return false;
            }
            
            // 记录UII信息
            StringBuilder uiiStr = new StringBuilder();
            for (byte b : uii) {
                uiiStr.append(String.format("%02X ", b));
            }
            logMessage("扫描到标签UII: " + uiiStr.toString().trim());
            
            // 确保扫描已完全关闭后再进行写入操作
            try {
                logMessage("等待100ms确保扫描完全关闭");
                Thread.sleep(100);
                logMessage("等待完成");
            } catch (InterruptedException e) {
                logMessage("等待被中断: " + e.getMessage());
                // 忽略中断异常
            }
            
            // 写入数据
            logMessage("开始写入数据到标签，内容: " + dataToWrite + ", 存储区: " + bank + ", 起始地址: " + startAddr);
            boolean result = writeToTag(uii, dataToWrite, bank, startAddr);
            logMessage("写入操作完成，结果: " + (result ? "成功" : "失败"));
            return result;
        } catch (RFIDException e) {
            Log.e(TAG, "RFID操作异常: " + e.getErrorCode() + ", " + e.getMessage());
            logMessage("RFID操作异常: 错误码=" + e.getErrorCode() + ", 消息=" + e.getMessage());
            throw e;
        } catch (Exception e) {
            Log.e(TAG, "扫描写入过程异常: " + e.getMessage());
            logMessage("扫描写入过程异常: " + e.getMessage());
            // 修改为使用正确的构造器
            throw new RFIDException(e);
        }
    }

    // RFID数据回调接口实现
    @Override
    public void onRFIDDataReceived(CommScanner scanner, RFIDDataReceivedEvent event) {

        handler.post(new Runnable() {
            @Override
            public void run() {
                // 处理接收到的数据
                
                for (int i = 0; i < event.getRFIDData().size(); i++) {
                    String data = "";
                    lastScannedUii = event.getRFIDData().get(i).getUII();
                }

            }
        });

    }

    private void stopScanning() {
        if (isScanning) {
            logMessage("开始停止扫描...");
            try {
                rfidScanner.close();
                logMessage("扫描器已关闭");
                rfidScanner.setDataDelegate(null);
                logMessage("数据委托已移除");
            } catch (RFIDException e) {
                Log.w(TAG, "关闭扫描时出错", e);
                logMessage("关闭扫描时出错: " + e.getMessage());
            }
            isScanning = false;
            logMessage("扫描状态已重置为false");
        } else {
            logMessage("当前不在扫描状态，无需停止");
        }
    }

    public void release() {
        logMessage("开始释放RFID读写帮助类资源...");
        stopScanning();
        if (rfidScanner != null) {
            try {
                rfidScanner.close();
                logMessage("扫描器已完全关闭");
            } catch (RFIDException e) {
                Log.w(TAG, "释放资源时出错", e);
                logMessage("释放资源时出错: " + e.getMessage());
            }
        } else {
            logMessage("扫描器实例为null，无需关闭");
        }
        logMessage("RFID读写帮助类资源释放完成");
    }
}
