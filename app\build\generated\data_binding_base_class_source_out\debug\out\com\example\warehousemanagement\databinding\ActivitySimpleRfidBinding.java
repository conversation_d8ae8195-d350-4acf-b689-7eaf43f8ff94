// Generated by view binder compiler. Do not edit!
package com.example.warehousemanagement.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager2.widget.ViewPager2;
import com.example.warehousemanagement.R;
import com.google.android.material.tabs.TabLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySimpleRfidBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnConnect;

  @NonNull
  public final Button btnWriteTag;

  @NonNull
  public final EditText etWriteContent;

  @NonNull
  public final TabLayout tabLayout;

  @NonNull
  public final TextView tvConnectionStatus;

  @NonNull
  public final ViewPager2 viewPager;

  private ActivitySimpleRfidBinding(@NonNull LinearLayout rootView, @NonNull Button btnConnect,
      @NonNull Button btnWriteTag, @NonNull EditText etWriteContent, @NonNull TabLayout tabLayout,
      @NonNull TextView tvConnectionStatus, @NonNull ViewPager2 viewPager) {
    this.rootView = rootView;
    this.btnConnect = btnConnect;
    this.btnWriteTag = btnWriteTag;
    this.etWriteContent = etWriteContent;
    this.tabLayout = tabLayout;
    this.tvConnectionStatus = tvConnectionStatus;
    this.viewPager = viewPager;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySimpleRfidBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySimpleRfidBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_simple_rfid, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySimpleRfidBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_connect;
      Button btnConnect = ViewBindings.findChildViewById(rootView, id);
      if (btnConnect == null) {
        break missingId;
      }

      id = R.id.btn_write_tag;
      Button btnWriteTag = ViewBindings.findChildViewById(rootView, id);
      if (btnWriteTag == null) {
        break missingId;
      }

      id = R.id.et_write_content;
      EditText etWriteContent = ViewBindings.findChildViewById(rootView, id);
      if (etWriteContent == null) {
        break missingId;
      }

      id = R.id.tab_layout;
      TabLayout tabLayout = ViewBindings.findChildViewById(rootView, id);
      if (tabLayout == null) {
        break missingId;
      }

      id = R.id.tv_connection_status;
      TextView tvConnectionStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvConnectionStatus == null) {
        break missingId;
      }

      id = R.id.view_pager;
      ViewPager2 viewPager = ViewBindings.findChildViewById(rootView, id);
      if (viewPager == null) {
        break missingId;
      }

      return new ActivitySimpleRfidBinding((LinearLayout) rootView, btnConnect, btnWriteTag,
          etWriteContent, tabLayout, tvConnectionStatus, viewPager);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
