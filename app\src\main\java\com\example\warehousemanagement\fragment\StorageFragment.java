package com.example.warehousemanagement.fragment;

import android.app.DatePickerDialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.example.warehousemanagement.R;
import com.example.warehousemanagement.activity.StorageAddActivity;
import com.example.warehousemanagement.adapter.RecordAdapter;
import com.example.warehousemanagement.databinding.FragmentStorageBinding;
import com.example.warehousemanagement.model.ApiResponse;
import com.example.warehousemanagement.model.RfidTag;
import com.example.warehousemanagement.network.ApiClient;
import com.example.warehousemanagement.network.ApiService;
import com.example.warehousemanagement.utils.DateUtils;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 入库Fragment
 * 功能：显示入库记录列表，提供筛选和添加功能
 */
public class StorageFragment extends Fragment {
    
    private FragmentStorageBinding binding;
    private ApiService apiService;
    private RecordAdapter adapter;
    private List<RfidTag> recordList;
    
    // 筛选条件
    private String startDate = "";
    private String endDate = "";
    private String selectedItemType = "";
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentStorageBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews();
        initRecyclerView();
        loadData();
    }
    
    /**
     * 初始化视图
     */
    private void initViews() {
        apiService = ApiClient.getInstance().getApiService();
        
        // 设置物品类型下拉框
        setupItemTypeSpinner();
        
        // 设置日期选择
        setupDatePickers();
        
        // 设置按钮点击事件
        binding.btnSearch.setOnClickListener(v -> performSearch());
        binding.fabAddStorage.setOnClickListener(v -> openAddStorageActivity());
    }
    
    /**
     * 设置物品类型下拉框
     */
    private void setupItemTypeSpinner() {
        String[] itemTypes = {
            getString(R.string.all_types),
            getString(R.string.item_type_box),
            getString(R.string.item_type_bag)
        };
        
        ArrayAdapter<String> adapter = new ArrayAdapter<>(
            requireContext(),
            android.R.layout.simple_spinner_item,
            itemTypes
        );
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        binding.spinnerItemType.setAdapter(adapter);
    }
    
    /**
     * 设置日期选择器
     */
    private void setupDatePickers() {
        binding.etStartDate.setOnClickListener(v -> showDatePicker(true));
        binding.etEndDate.setOnClickListener(v -> showDatePicker(false));
    }
    
    /**
     * 显示日期选择器
     */
    private void showDatePicker(boolean isStartDate) {
        Calendar calendar = Calendar.getInstance();
        
        DatePickerDialog datePickerDialog = new DatePickerDialog(
            requireContext(),
            (view, year, month, dayOfMonth) -> {
                calendar.set(year, month, dayOfMonth);
                String selectedDate = DateUtils.formatDate(calendar.getTime());
                
                if (isStartDate) {
                    startDate = selectedDate;
                    binding.etStartDate.setText(selectedDate);
                } else {
                    endDate = selectedDate;
                    binding.etEndDate.setText(selectedDate);
                }
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        );
        
        datePickerDialog.show();
    }
    
    /**
     * 初始化RecyclerView
     */
    private void initRecyclerView() {
        recordList = new ArrayList<>();
        adapter = new RecordAdapter(recordList);
        
        binding.rvStorageList.setLayoutManager(new LinearLayoutManager(requireContext()));
        binding.rvStorageList.setAdapter(adapter);
    }
    
    /**
     * 加载数据
     */
    private void loadData() {
        performSearch();
    }
    
    /**
     * 执行搜索
     */
    private void performSearch() {
        String remark = binding.etRemarkFilter.getText().toString().trim();
        
        // 获取选中的物品类型
        int selectedPosition = binding.spinnerItemType.getSelectedItemPosition();
        String tagType = null;
        if (selectedPosition == 1) {
            tagType = "x"; // 箱子
        } else if (selectedPosition == 2) {
            tagType = "d"; // 袋子
        }
        
        // 发送网络请求
        Call<ApiResponse<List<RfidTag>>> call = apiService.getStorageRecords(
            startDate.isEmpty() ? null : startDate,
            endDate.isEmpty() ? null : endDate,
            tagType,
            remark.isEmpty() ? null : remark
        );
        
        call.enqueue(new Callback<ApiResponse<List<RfidTag>>>() {
            @Override
            public void onResponse(Call<ApiResponse<List<RfidTag>>> call, Response<ApiResponse<List<RfidTag>>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<RfidTag>> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        updateRecordList(apiResponse.getData());
                    } else {
                        showError(apiResponse.getMessage());
                    }
                } else {
                    showError("查询失败，请检查网络连接");
                }
            }
            
            @Override
            public void onFailure(Call<ApiResponse<List<RfidTag>>> call, Throwable t) {
                showError("网络错误：" + t.getMessage());
            }
        });
    }
    
    /**
     * 更新记录列表
     */
    private void updateRecordList(List<RfidTag> newRecords) {
        recordList.clear();
        if (newRecords != null) {
            recordList.addAll(newRecords);
        }
        adapter.notifyDataSetChanged();
    }
    
    /**
     * 打开添加入库Activity
     */
    private void openAddStorageActivity() {
        Intent intent = new Intent(requireContext(), StorageAddActivity.class);
        startActivity(intent);
    }
    
    /**
     * 显示错误信息
     */
    private void showError(String message) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void onResume() {
        super.onResume();
        // 刷新数据
        loadData();
    }
    
    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
