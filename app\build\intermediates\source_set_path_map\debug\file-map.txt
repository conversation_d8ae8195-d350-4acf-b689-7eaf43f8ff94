com.example.warehousemanagement.app-drawerlayout-1.1.1-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01101660e5b438fcb5e34360809c4a6f\transformed\drawerlayout-1.1.1\res
com.example.warehousemanagement.app-core-1.10.1-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03f312cdf143f005647525320abeeeeb\transformed\core-1.10.1\res
com.example.warehousemanagement.app-DENSOScannerSDK-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a87d070892527ef2ad2180afc02c330\transformed\DENSOScannerSDK\res
com.example.warehousemanagement.app-lifecycle-process-2.6.2-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ab6b896c30a65e2f99f3a0f6b17306d\transformed\lifecycle-process-2.6.2\res
com.example.warehousemanagement.app-lifecycle-runtime-ktx-2.6.2-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bc9b3656eb0ff573e034bb7c4097322\transformed\lifecycle-runtime-ktx-2.6.2\res
com.example.warehousemanagement.app-startup-runtime-1.1.1-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c326eb9560e6c7088bb51c1f229c5c3\transformed\startup-runtime-1.1.1\res
com.example.warehousemanagement.app-cardview-1.0.0-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2fe1ebeb6cfcc9526ce1c59b2d94d1cb\transformed\cardview-1.0.0\res
com.example.warehousemanagement.app-swiperefreshlayout-1.1.0-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36e49461ebab2cf1a6dc26d3bf8625f8\transformed\swiperefreshlayout-1.1.0\res
com.example.warehousemanagement.app-lifecycle-viewmodel-2.6.2-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\406dea3ec7637268551eaf96834176ca\transformed\lifecycle-viewmodel-2.6.2\res
com.example.warehousemanagement.app-emoji2-1.2.0-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\res
com.example.warehousemanagement.app-emoji2-views-helper-1.2.0-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\512443722c5b13d2bffe326918c64f45\transformed\emoji2-views-helper-1.2.0\res
com.example.warehousemanagement.app-viewpager2-1.0.0-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c57353fa174b699e2e469b00b81daae\transformed\viewpager2-1.0.0\res
com.example.warehousemanagement.app-profileinstaller-1.3.0-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\res
com.example.warehousemanagement.app-lifecycle-runtime-2.6.2-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f077d15c13cb87c91ee0f6a22e651b8\transformed\lifecycle-runtime-2.6.2\res
com.example.warehousemanagement.app-constraintlayout-2.1.4-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67140b26e163fc2341e0cafac27817ef\transformed\constraintlayout-2.1.4\res
com.example.warehousemanagement.app-appcompat-resources-1.6.1-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68da82d5067c8b58082a840cefff9791\transformed\appcompat-resources-1.6.1\res
com.example.warehousemanagement.app-core-runtime-2.2.0-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b077a8c37bcb4943e93a3f51f240112\transformed\core-runtime-2.2.0\res
com.example.warehousemanagement.app-activity-1.6.0-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92ac7cf4bad242c1ce0a7bb9825b65be\transformed\activity-1.6.0\res
com.example.warehousemanagement.app-annotation-experimental-1.3.0-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98f89881cfaf7e4a9e62fdc699ce48d9\transformed\annotation-experimental-1.3.0\res
com.example.warehousemanagement.app-core-ktx-1.10.1-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a649d2708da15118aab8d4827d3d5775\transformed\core-ktx-1.10.1\res
com.example.warehousemanagement.app-lifecycle-livedata-2.6.2-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0b63ca1e234b22d783426efd07a9f8c\transformed\lifecycle-livedata-2.6.2\res
com.example.warehousemanagement.app-coordinatorlayout-1.1.0-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b29c8d9d371a2a50c4728ab935c12eb6\transformed\coordinatorlayout-1.1.0\res
com.example.warehousemanagement.app-savedstate-1.2.1-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5f7f131f850296f1787d5900e89a13f\transformed\savedstate-1.2.1\res
com.example.warehousemanagement.app-customview-poolingcontainer-1.0.0-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c226b7556fe16c8f1017b1c066e7a9bb\transformed\customview-poolingcontainer-1.0.0\res
com.example.warehousemanagement.app-recyclerview-1.3.0-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c85f23af8b8280c69160e7a43ca2b8ec\transformed\recyclerview-1.3.0\res
com.example.warehousemanagement.app-lifecycle-livedata-core-2.6.2-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c91ba7ef297da0dffbd5c22ae0e65df9\transformed\lifecycle-livedata-core-2.6.2\res
com.example.warehousemanagement.app-lifecycle-service-2.6.2-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc7d66730bd57ce6ae44b2273400e700\transformed\lifecycle-service-2.6.2\res
com.example.warehousemanagement.app-lifecycle-viewmodel-savedstate-2.6.2-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e24575acf9e2b29336a45a38959754f7\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.example.warehousemanagement.app-fragment-1.5.7-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e257b58df5aaa0b6a17c3e0c8af554a9\transformed\fragment-1.5.7\res
com.example.warehousemanagement.app-databinding-adapters-7.4.2-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e53a61425c54436f1504c86f5b3bf331\transformed\databinding-adapters-7.4.2\res
com.example.warehousemanagement.app-material-1.9.0-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8cdd8f14c70e551ad9fcc8ba2fb4e74\transformed\material-1.9.0\res
com.example.warehousemanagement.app-databinding-runtime-7.4.2-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecec38a330cbac868441310d3b5aa30c\transformed\databinding-runtime-7.4.2\res
com.example.warehousemanagement.app-transition-1.2.0-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa703a8f114acc92c6f94126765217ee\transformed\transition-1.2.0\res
com.example.warehousemanagement.app-appcompat-1.6.1-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff72482a731973c8e0ce199a0d194ac5\transformed\appcompat-1.6.1\res
com.example.warehousemanagement.app-pngs-34 C:\Users\<USER>\Desktop\Box\app\build\generated\res\pngs\debug
com.example.warehousemanagement.app-resValues-35 C:\Users\<USER>\Desktop\Box\app\build\generated\res\resValues\debug
com.example.warehousemanagement.app-rs-36 C:\Users\<USER>\Desktop\Box\app\build\generated\res\rs\debug
com.example.warehousemanagement.app-packageDebugResources-37 C:\Users\<USER>\Desktop\Box\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.warehousemanagement.app-packageDebugResources-38 C:\Users\<USER>\Desktop\Box\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.warehousemanagement.app-merged_res-39 C:\Users\<USER>\Desktop\Box\app\build\intermediates\merged_res\debug
com.example.warehousemanagement.app-debug-40 C:\Users\<USER>\Desktop\Box\app\src\debug\res
com.example.warehousemanagement.app-main-41 C:\Users\<USER>\Desktop\Box\app\src\main\res
