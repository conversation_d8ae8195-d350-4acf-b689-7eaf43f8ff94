<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/CardStyle"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <!-- 记录ID -->
            <TextView
                android:id="@+id/tv_record_id"
                style="@style/SubtitleText"
                android:text="#001"
                android:layout_weight="1" />

            <!-- 状态标签 -->
            <TextView
                android:id="@+id/tv_status"
                style="@style/BodyText"
                android:text="已完成"
                android:textColor="@color/success"
                android:background="@drawable/status_background"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- 日期 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="4dp">

            <TextView
                style="@style/BodyText"
                android:text="日期："
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tv_date"
                style="@style/BodyText"
                android:text="2024-01-01" />

        </LinearLayout>

        <!-- 客户名称（发货和回收时显示） -->
        <LinearLayout
            android:id="@+id/ll_customer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="4dp"
            android:visibility="gone">

            <TextView
                style="@style/BodyText"
                android:text="客户："
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tv_customer"
                style="@style/BodyText"
                android:text="客户名称" />

        </LinearLayout>

        <!-- 备注1 -->
        <LinearLayout
            android:id="@+id/ll_remark1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="4dp"
            android:visibility="gone">

            <TextView
                style="@style/BodyText"
                android:text="备注1："
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tv_remark1"
                style="@style/BodyText"
                android:text="备注内容" />

        </LinearLayout>

        <!-- 备注2 -->
        <LinearLayout
            android:id="@+id/ll_remark2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp"
            android:visibility="gone">

            <TextView
                style="@style/BodyText"
                android:text="备注2："
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tv_remark2"
                style="@style/BodyText"
                android:text="备注内容" />

        </LinearLayout>

        <!-- 标签数量 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                style="@style/BodyText"
                android:text="标签数量："
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tv_tag_count"
                style="@style/BodyText"
                android:text="0"
                android:textColor="@color/primary" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>