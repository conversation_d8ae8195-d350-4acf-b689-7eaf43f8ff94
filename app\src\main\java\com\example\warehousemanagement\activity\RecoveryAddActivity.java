package com.example.warehousemanagement.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.example.warehousemanagement.R;
import com.example.warehousemanagement.adapter.RecoveryTagAdapter;
import com.example.warehousemanagement.databinding.ActivityRecoveryAddBinding;
import com.example.warehousemanagement.model.ApiResponse;
import com.example.warehousemanagement.model.RecoveryRequest;
import com.example.warehousemanagement.model.RfidTag;
import com.example.warehousemanagement.network.ApiClient;
import com.example.warehousemanagement.network.ApiService;
import com.example.warehousemanagement.rfid.RfidManager;
import com.example.warehousemanagement.utils.DateUtils;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 回收添加Activity
 * 功能：扫描RFID标签并添加回收记录，显示保质期信息
 */
public class RecoveryAddActivity extends AppCompatActivity implements RfidManager.RfidDataListener, RfidManager.RfidStatusListener {

    private ActivityRecoveryAddBinding binding;
    private RfidManager rfidManager;
    private ApiService apiService;
    private RecoveryTagAdapter adapter;
    private List<RfidTag> scannedTags;
    private Set<String> tagIdSet; // 用于去重

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityRecoveryAddBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        initViews();
        initRfid();
        initRecyclerView();
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        apiService = ApiClient.getInstance().getApiService();

        // 设置工具栏
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("添加回收");
        }

        // 设置当前日期
        binding.etRecoveryDate.setText(DateUtils.getCurrentDate());

        // 设置按钮点击事件
        binding.btnScan.setOnClickListener(v -> toggleScanning());
        binding.btnSave.setOnClickListener(v -> saveRecoveryRecords());
    }

    /**
     * 初始化RFID
     */
    private void initRfid() {
        rfidManager = RfidManager.getInstance();
        rfidManager.addDataListener(this);
        rfidManager.addStatusListener(this);

        // 更新连接状态
        updateConnectionStatus();
    }

    /**
     * 初始化RecyclerView
     */
    private void initRecyclerView() {
        scannedTags = new ArrayList<>();
        tagIdSet = new HashSet<>();
        adapter = new RecoveryTagAdapter(scannedTags);

        // 设置删除监听器
        adapter.setOnDeleteClickListener(position -> {
            if (position >= 0 && position < scannedTags.size()) {
                RfidTag removedTag = scannedTags.remove(position);
                tagIdSet.remove(removedTag.getTagId());
                adapter.notifyItemRemoved(position);
                updateTagCount();
            }
        });

        binding.rvScannedTags.setLayoutManager(new LinearLayoutManager(this));
        binding.rvScannedTags.setAdapter(adapter);

        updateTagCount();
    }

    /**
     * 切换扫描状态
     */
    private void toggleScanning() {
        if (!rfidManager.isConnected()) {
            Toast.makeText(this, "RFID设备未连接", Toast.LENGTH_SHORT).show();
            return;
        }

        if (rfidManager.isScanning()) {
            rfidManager.stopScanning();
        } else {
            rfidManager.startScanning();
        }
    }

    /**
     * 查询标签信息
     */
    private void queryTagInfo(String tagId) {
        Call<ApiResponse<RfidTag>> call = apiService.getTagInfo(tagId);
        call.enqueue(new Callback<ApiResponse<RfidTag>>() {
            @Override
            public void onResponse(Call<ApiResponse<RfidTag>> call, Response<ApiResponse<RfidTag>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<RfidTag> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        RfidTag tagInfo = apiResponse.getData();
                        addScannedTag(tagInfo);
                    } else {
                        // 如果查询不到标签信息，创建一个基本的标签对象
                        RfidTag basicTag = new RfidTag();
                        basicTag.setTagId(tagId);
                        basicTag.setTagStatus("未知");
                        addScannedTag(basicTag);
                    }
                } else {
                    // 网络错误时也创建基本标签对象
                    RfidTag basicTag = new RfidTag();
                    basicTag.setTagId(tagId);
                    basicTag.setTagStatus("查询失败");
                    addScannedTag(basicTag);
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<RfidTag>> call, Throwable t) {
                // 网络错误时创建基本标签对象
                RfidTag basicTag = new RfidTag();
                basicTag.setTagId(tagId);
                basicTag.setTagStatus("网络错误");
                addScannedTag(basicTag);
            }
        });
    }

    /**
     * 添加扫描到的标签
     */
    private void addScannedTag(RfidTag tag) {
        runOnUiThread(() -> {
            scannedTags.add(tag);
            adapter.notifyItemInserted(scannedTags.size() - 1);
            updateTagCount();
        });
    }

    /**
     * 保存回收记录
     */
    private void saveRecoveryRecords() {
        if (scannedTags.isEmpty()) {
            Toast.makeText(this, "请先扫描标签", Toast.LENGTH_SHORT).show();
            return;
        }

        // 显示加载状态
        binding.btnSave.setEnabled(false);
        binding.btnSave.setText("保存中...");

        // 创建回收请求
        List<RecoveryRequest.RecoveryTag> tags = new ArrayList<>();
        String remark1 = binding.etRemark1.getText().toString().trim();
        String remark2 = binding.etRemark2.getText().toString().trim();

        for (RfidTag scannedTag : scannedTags) {
            RecoveryRequest.RecoveryTag tag = new RecoveryRequest.RecoveryTag(scannedTag.getTagId());
            if (!remark1.isEmpty()) {
                tag.setRemark1(remark1);
            }
            if (!remark2.isEmpty()) {
                tag.setRemark2(remark2);
            }
            tags.add(tag);
        }

        RecoveryRequest request = new RecoveryRequest(DateUtils.getCurrentDate(), tags);

        // 发送网络请求
        Call<ApiResponse<Void>> call = apiService.addRecoveryRecords(request);
        call.enqueue(new Callback<ApiResponse<Void>>() {
            @Override
            public void onResponse(Call<ApiResponse<Void>> call, Response<ApiResponse<Void>> response) {
                binding.btnSave.setEnabled(true);
                binding.btnSave.setText("保存");

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<Void> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        Toast.makeText(RecoveryAddActivity.this, "回收成功", Toast.LENGTH_SHORT).show();
                        finish();
                    } else {
                        Toast.makeText(RecoveryAddActivity.this, apiResponse.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                } else {
                    Toast.makeText(RecoveryAddActivity.this, "回收失败，请检查网络连接", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<Void>> call, Throwable t) {
                binding.btnSave.setEnabled(true);
                binding.btnSave.setText("保存");
                Toast.makeText(RecoveryAddActivity.this, "网络错误：" + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * 更新标签数量显示
     */
    private void updateTagCount() {
        // 可以在这里添加标签数量显示逻辑
    }

    /**
     * 更新连接状态
     */
    private void updateConnectionStatus() {
        if (rfidManager.isConnected()) {
            binding.tvConnectionStatus.setText("设备状态: 已连接");
            binding.btnScan.setEnabled(true);
        } else {
            binding.tvConnectionStatus.setText("设备状态: 未连接");
            binding.btnScan.setEnabled(false);
        }
    }

    /**
     * 更新扫描状态
     */
    private void updateScanningStatus() {
        if (rfidManager.isScanning()) {
            binding.btnScan.setText("停止扫描");
        } else {
            binding.btnScan.setText("开始扫描");
        }
    }

    // ========== RFID监听器实现 ==========

    @Override
    public void onRfidDataReceived(List<String> tagIds) {
        for (String tagId : tagIds) {
            if (!tagIdSet.contains(tagId)) {
                tagIdSet.add(tagId);
                // 查询标签详细信息
                queryTagInfo(tagId);
            }
        }
    }

    @Override
    public void onConnectionStatusChanged(boolean connected) {
        runOnUiThread(this::updateConnectionStatus);
    }

    @Override
    public void onScanningStatusChanged(boolean scanning) {
        runOnUiThread(this::updateScanningStatus);
    }

    @Override
    public void onError(String error) {
        runOnUiThread(() -> Toast.makeText(this, error, Toast.LENGTH_SHORT).show());
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateConnectionStatus();
        updateScanningStatus();
    }

    @Override
    protected void onPause() {
        super.onPause();
        // 停止扫描
        if (rfidManager.isScanning()) {
            rfidManager.stopScanning();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 移除监听器
        rfidManager.removeDataListener(this);
        rfidManager.removeStatusListener(this);
        binding = null;
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}
