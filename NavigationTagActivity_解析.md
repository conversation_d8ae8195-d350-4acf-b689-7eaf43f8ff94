# NavigationTagActivity.java 详细解析

## 类概述
`NavigationTagActivity` 是一个用于控制和管理 RFID 标签的 Android 活动类，继承自 `BaseActivity` 并实现了 `BarcodeDataDelegate` 接口。该类主要功能是通过 DENSO 扫描器 SDK 与 RFID 标签进行交互，实现标签的搜索、LED 灯控制（开/关）以及标签信息的管理。

## 成员变量

### 基本变量
- `uii`：存储 RFID 标签的 UII (Unique Item Identifier) 字符串
- `workserialNo`：当前工作的序列号
- `serialArray`：存储序列号的 ArrayList
- `light_mode`：灯光模式（0：按钮模式，1：触发器模式）
- `scannerConnectedOnCreate`：标记在创建活动时是否已连接扫描器

### 常量定义
- `LIGHT_ON`/`LIGHT_OFF`：灯光开关状态常量（1/0）
- `DB_TAG_*`：数据库相关常量，定义了电池状态、灯光状态等
- `DB_SERIALNO`/`DB_LIGHT_ON_TIMER`：数据库字段位置常量
- `TIMER_LIGHT_CONTROL_INTERVAL`：定时器控制间隔

### UI 元素
- 各种 TextView、EditText、Button 控件，用于显示操作信息、输入标签 ID、控制灯光等

### 操作状态记录
- `bBarcodeOpen`：条码扫描器开启状态
- `bRFIDOpen`：RFID 扫描器开启状态
- `bBarcodeSetDataDelegate`/`bRFIDSetDataDelegate`：数据委托设置状态
- `pauseMainActivity`：活动生命周期状态

### 扫描器设置
- `mRFIDScannerSettings`：RFID 扫描器设置
- `mBarcodeScannerSettings`：条码扫描器设置
- `mCommScannerBtSettings`：蓝牙设置
- `mCommScannerParams`：通信参数
- `mRFIDScanner`/`mBarcodeScanner`/`mScanner`：扫描器实例

## 主要方法解析

### 生命周期方法

#### onCreate(Bundle savedInstanceState)
```java
protected void onCreate(Bundle savedInstanceState)
```
- **功能**：活动创建时初始化 UI、连接扫描器、设置监听器
- **实现**：
  1. 设置布局和初始化状态变量
  2. 检查扫描器连接状态
  3. 如果已连接，获取扫描器实例并设置数据委托
  4. 初始化数据库
  5. 初始化 UI 元素并设置监听器
  6. 创建定义文件的文件夹
  7. 设置初始灯光模式

#### onResume()
```java
protected void onResume()
```
- **功能**：活动恢复时重新初始化扫描器和 UI
- **实现**：
  1. 如果从其他活动返回（pauseMainActivity == true）
  2. 打开条码扫描器并设置数据委托
  3. 重置 UI 显示

#### onPause()
```java
protected void onPause()
```
- **功能**：活动暂停时关闭扫描器和释放资源
- **实现**：
  1. 关闭条码扫描器和 RFID 扫描器
  2. 移除数据委托
  3. 设置 pauseMainActivity 标志

#### onRestart()
```java
protected void onRestart()
```
- **功能**：活动重启时设置 disposeFlg 标志

#### onDestroy()
```java
protected void onDestroy()
```
- **功能**：活动销毁时断开扫描器连接

#### onUserLeaveHint()
```java
protected void onUserLeaveHint()
```
- **功能**：用户离开活动时停止后台读取

### 导航和 UI 控制方法

#### navigateUp()
```java
private void navigateUp()
```
- **功能**：返回上一级活动（MainActivity）
- **实现**：
  1. 调用 onPause() 方法
  2. 释放 RFID 扫描器的数据委托
  3. 备份扫描器设置
  4. 设置 disposeFlg 为 false
  5. 启动 MainActivity 并结束当前活动

#### focusleave(View view)
```java
public void focusleave(View view)
```
- **功能**：处理输入框失去焦点的事件
- **实现**：
  1. 隐藏软键盘
  2. 检查输入的 ID 并搜索对应的序列号
  3. 根据检查结果更新 UI 消息
  4. 如果 ID 有效，搜索对应的 RFID 标签

#### onTouchEvent(MotionEvent event)
```java
public boolean onTouchEvent(MotionEvent event)
```
- **功能**：处理屏幕触摸事件，将焦点转移到虚拟焦点控件

### 按钮点击处理方法

#### onclick_ButtonMode(View view)
```java
public void onclick_ButtonMode(View view)
```
- **功能**：切换到按钮模式
- **实现**：
  1. 如果当前不是按钮模式，停止触发器模式的灯光控制
  2. 设置 light_mode 为 0
  3. 更新按钮颜色和可见性

#### onclick_TriggerMode(View view)
```java
public void onclick_TriggerMode(View view)
```
- **功能**：切换到触发器模式
- **实现**：
  1. 如果当前不是触发器模式，设置 light_mode 为 1
  2. 更新按钮颜色和可见性
  3. 调用 LightOnTriggerButton_onclick 方法

#### onclick_RegisterTAG(View view)
```java
public void onclick_RegisterTAG(View view)
```
- **功能**：跳转到标签注册活动

#### onclick_DisplayTAG(View view)
```java
public void onclick_DisplayTAG(View view)
```
- **功能**：跳转到标签显示活动

#### LightOnButton_onclick(View view)
```java
public void LightOnButton_onclick(View view)
```
- **功能**：打开所有选定标签的 LED 灯
- **实现**：
  1. 关闭条码扫描器
  2. 遍历 serialArray 中的所有序列号
  3. 对每个序列号调用 writeTagWithLightOnOff 方法打开灯光
  4. 更新操作消息

#### LightOnTriggerButton_onclick(View view)
```java
public void LightOnTriggerButton_onclick(View view)
```
- **功能**：在触发器模式下打开当前工作标签的 LED 灯
- **实现**：
  1. 关闭条码扫描器
  2. 如果 workserialNo 不为空，调用 writeTagWithLightOnOff 方法打开灯光
  3. 更新操作消息

#### LightOffButton_onclick(View view)
```java
public void LightOffButton_onclick(View view)
```
- **功能**：关闭所有选定标签的 LED 灯
- **实现**：
  1. 关闭条码扫描器
  2. 遍历 serialArray 中的所有序列号
  3. 对每个序列号调用 writeTagWithLightOnOff 方法关闭灯光
  4. 更新操作消息

### RFID 和条码操作方法

#### onBarcodeDataReceived(CommScanner commScanner, BarcodeDataReceivedEvent barcodeEvent)
```java
public void onBarcodeDataReceived(CommScanner commScanner, BarcodeDataReceivedEvent barcodeEvent)
```
- **功能**：处理从扫描器接收到的条码数据
- **实现**：
  1. 获取条码数据并转换为字符串
  2. 检查条码类型（序列号、ID 或无效）
  3. 更新 UI 消息并搜索对应的 RFID 标签

#### check_serialno_ID(String snumber)
```java
int check_serialno_ID(String snumber)
```
- **功能**：检查输入的字符串是序列号还是 ID
- **实现**：
  1. 根据字符串长度判断类型
  2. 如果是 24 位，尝试从 UII 设置序列号
  3. 如果是 5 位或更少，尝试从 ID 设置序列号
  4. 返回类型代码（1：序列号，2：ID，3：无效，4：未注册序列号，5：未注册 ID）

#### updatePromptMessageA(int imessage_type, String sworkserialno, String sworkID)
```java
private void updatePromptMessageA(int imessage_type, String sworkserialno, String sworkID)
```
- **功能**：根据消息类型更新提示消息
- **实现**：根据不同的消息类型（序列号、ID、无效等）设置不同的提示文本

#### writeTagWithLightOnOff(String serialno, int onoff)
```java
private int writeTagWithLightOnOff(String serialno, int onoff)
```
- **功能**：控制指定序列号标签的 LED 灯开关
- **实现**：
  1. 将序列号转换为完整的 UII 格式
  2. 根据 onoff 参数和当前灯光模式执行不同操作：
     - 按钮模式下打开灯光：调用 lightOn 方法并启动倒计时
     - 触发器模式下打开灯光：调用 startLightOnByTrigger 方法
     - 关闭灯光：调用 lightOff 方法
  3. 返回操作结果（1：成功，0：失败）

#### stringToByte(String hex)
```java
private byte[] stringToByte(String hex)
```
- **功能**：将十六进制字符串转换为字节数组
- **实现**：每两个字符转换为一个字节

#### byteToString(byte[] bytes)
```java
private String byteToString(byte[] bytes)
```
- **功能**：将字节数组转换为十六进制字符串
- **实现**：每个字节转换为两个十六进制字符

### 扫描器设置方法

#### getallsettings()
```java
public void getallsettings()
```
- **功能**：获取并设置所有扫描器参数
- **实现**：
  1. 获取并备份通信参数、蓝牙设置、条码扫描器设置和 RFID 扫描器设置
  2. 调用相应的设置方法修改设置
  3. 将修改后的设置应用到扫描器

#### setSettingsParams()
```java
private void setSettingsParams()
```
- **功能**：设置通信参数
- **实现**：设置蓝牙重连模式和蜂鸣器音量

#### setSettingsBT()
```java
public void setSettingsBT()
```
- **功能**：设置蓝牙参数
- **实现**：设置主设备尝试时间

#### setSettingsBarcodeScanner()
```java
public void setSettingsBarcodeScanner()
```
- **功能**：设置条码扫描器参数
- **实现**：
  1. 设置触发模式、灯光模式、标记模式和侧灯模式
  2. 禁用大多数条码类型
  3. 启用 QR 码相关设置

#### setSettingsRFIDScanner()
```java
public void setSettingsRFIDScanner()
```
- **功能**：设置 RFID 扫描器参数
- **实现**：设置触发模式、读写功率、Q 参数、会话标志等

### 数据库操作方法

#### setSerialNoFromID(String sworkID)
```java
int setSerialNoFromID(String sworkID)
```
- **功能**：根据 ID 设置对应的序列号到 serialArray
- **实现**：
  1. 将输入的 ID 格式化为 5 位数
  2. 构建数据库查询条件
  3. 调用 setSelecteItems 方法执行查询并设置结果
  4. 返回找到的记录数

#### setSerialNoFromUII(String sworkUII)
```java
int setSerialNoFromUII(String sworkUII)
```
- **功能**：根据 UII 设置对应的序列号到 serialArray
- **实现**：
  1. 验证 UII 格式是否正确
  2. 提取序列号部分
  3. 构建数据库查询条件
  4. 调用 setSelecteItems 方法执行查询并设置结果
  5. 返回找到的记录数或错误代码

#### setSelecteItems(String selparams, String[] params)
```java
int setSelecteItems(String selparams, String[] params)
```
- **功能**：根据条件从数据库查询记录并设置序列号到 serialArray
- **实现**：
  1. 执行数据库查询
  2. 清空 serialArray
  3. 如果找到记录，将序列号添加到 serialArray
  4. 返回找到的记录数

#### serialtagclear()
```java
private void serialtagclear()
```
- **功能**：清空 serialArray

#### serialtagadd(String sserialno)
```java
private void serialtagadd(String sserialno)
```
- **功能**：向 serialArray 添加序列号

#### setButtonVisibleInit()
```java
private void setButtonVisibleInit()
```
- **功能**：初始化按钮可见性（启动时等待连接扫描器）

#### setButtonVisibleAfterConnect()
```java
private void setButtonVisibleAfterConnect()
```
- **功能**：连接扫描器后设置按钮可见性

#### setButtonVisibleAfterTagNumberEntry()
```java
private void setButtonVisibleAfterTagNumberEntry()
```
- **功能**：输入标签号后设置按钮可见性

#### dbupdatebattery(String serialNo, int battery, int light)
```java
private void dbupdatebattery(String serialNo, int battery, int light)
```
- **功能**：更新数据库中指定序列号标签的电池状态和灯光状态

#### dbupdatelight(String serialNo, int light)
```java
private void dbupdatelight(String serialNo, int light)
```
- **功能**：更新数据库中指定序列号标签的灯光状态和计时器

#### setdisplayline(String serialno)
```java
private String setdisplayline(String serialno)
```
- **功能**：获取指定序列号标签的信息并格式化为显示文本
- **实现**：
  1. 从数据库查询标签信息
  2. 格式化序列号和 ID
  3. 格式化灯光状态和电池状态
  4. 返回格式化后的 HTML 文本

#### taglightoffControl()
```java
private void taglightoffControl()
```
- **功能**：控制标签灯光的自动关闭
- **实现**：
  1. 查询数据库中灯光开启且计时器大于 0 的标签
  2. 更新计时器值，减少指定间隔
  3. 如果计时器归零，关闭标签灯光并更新数据库

### 扫描器控制方法

#### closebarcode()
```java
int closebarcode()
```
- **功能**：关闭条码扫描器
- **实现**：如果扫描器已打开，调用 closeReader 方法关闭

#### closerfid()
```java
int closerfid()
```
- **功能**：关闭 RFID 扫描器
- **实现**：
  1. 停止触发器模式的灯光控制
  2. 如果扫描器已打开，调用 close 方法关闭

#### openbarcode()
```java
int openbarcode()
```
- **功能**：打开条码扫描器
- **实现**：如果扫描器未打开，调用 openReader 方法打开

#### searchRFID(final ArrayList<String> serialarray)
```java
public void searchRFID(final ArrayList<String> serialarray)
```
- **功能**：搜索指定序列号的 RFID 标签
- **实现**：
  1. 清空 workserialNo
  2. 在后台线程中执行搜索
  3. 对每个序列号，尝试获取标签状态
  4. 处理获取到的标签状态信息
  5. 完成后重新打开条码扫描器并更新消息

#### tagStatus(String[] data)
```java
public void tagStatus(String[] data)
```
- **功能**：处理标签状态信息
- **实现**：
  1. 从 UII 中提取序列号
  2. 解析电池和灯光状态
  3. 更新数据库
  4. 在 UI 线程中更新显示

### 辅助方法

#### getAppStorageDirPath(Context context)
```java
public static String getAppStorageDirPath(Context context)
```
- **功能**：获取应用存储目录路径，如果不存在则创建
- **实现**：
  1. 获取外部文件目录
  2. 如果目录不存在，创建目录
  3. 返回目录的绝对路径

#### CountDown 内部类
```java
class CountDown extends CountDownTimer
```
- **功能**：实现倒计时功能，用于控制标签灯光的自动关闭
- **方法**：
  - `onFinish()`：倒计时结束时调用 taglightoffControl 方法
  - `onTick(long millisUntilFinished)`：倒计时过程中的回调（此处未实现具体功能）

#### onKeyDown(int keyCode, KeyEvent event)
```java
public boolean onKeyDown(int keyCode, KeyEvent event)
```
- **功能**：处理按键事件，特别是返回键
- **实现**：
  1. 如果是返回键，释放资源并结束活动
  2. 否则调用父类方法处理

## 总结

`NavigationTagActivity` 类是一个复杂的 Android 活动，主要用于与 RFID 标签交互，特别是控制标签的 LED 灯。它通过 DENSO 扫描器 SDK 与硬件通信，使用 SQLite 数据库存储标签信息，并提供了用户友好的界面来管理标签。

主要功能包括：
1. 通过条码扫描器读取标签 QR 码或手动输入 ID
2. 搜索对应的 RFID 标签
3. 控制标签 LED 灯的开关（按钮模式和触发器模式）
4. 显示标签信息（序列号、ID、灯光状态、电池状态）
5. 管理标签数据库

该类展示了 Android 应用与 RFID 硬件交互的完整实现，包括设备连接、数据传输、用户界面和数据持久化等方面。