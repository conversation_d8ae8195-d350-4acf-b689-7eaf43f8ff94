package com.example.warehousemanagement.network;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import java.io.IOException;

/**
 * Session拦截器
 * 处理HTTP Session管理
 */
public class SessionInterceptor implements Interceptor {
    
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request originalRequest = chain.request();
        
        // 添加必要的请求头
        Request.Builder requestBuilder = originalRequest.newBuilder()
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept", "application/json");
        
        Request newRequest = requestBuilder.build();
        Response response = chain.proceed(newRequest);
        
        // 处理响应，保存Session信息等
        return response;
    }
}
