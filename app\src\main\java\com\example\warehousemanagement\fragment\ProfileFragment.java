package com.example.warehousemanagement.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.example.warehousemanagement.activity.LoginActivity;
import com.example.warehousemanagement.activity.SettingsActivity;
import com.example.warehousemanagement.databinding.FragmentProfileBinding;
import com.example.warehousemanagement.model.ApiResponse;
import com.example.warehousemanagement.model.User;
import com.example.warehousemanagement.network.ApiClient;
import com.example.warehousemanagement.network.ApiService;
import com.example.warehousemanagement.utils.PreferenceManager;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 个人中心Fragment
 * 功能：显示用户信息，提供设置和退出登录功能
 */
public class ProfileFragment extends Fragment {

    private FragmentProfileBinding binding;
    private ApiService apiService;
    private PreferenceManager preferenceManager;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentProfileBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews();
        loadUserInfo();
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        apiService = ApiClient.getInstance().getApiService();
        preferenceManager = PreferenceManager.getInstance(requireContext());

        // 设置按钮点击事件
        binding.llSettings.setOnClickListener(v -> openSettings());
        binding.btnLogout.setOnClickListener(v -> performLogout());
    }

    /**
     * 加载用户信息
     */
    private void loadUserInfo() {
        User user = preferenceManager.getUserInfo();
        if (user != null) {
            displayUserInfo(user);
        }
    }

    /**
     * 显示用户信息
     */
    private void displayUserInfo(User user) {
        binding.tvUsername.setText(user.getUsername());
        binding.tvRole.setText(user.getRole() != null ? user.getRole() : "普通用户");
    }

    /**
     * 打开设置页面
     */
    private void openSettings() {
        Intent intent = new Intent(requireContext(), SettingsActivity.class);
        startActivity(intent);
    }

    /**
     * 执行退出登录
     */
    private void performLogout() {
        // 显示加载状态
        binding.btnLogout.setEnabled(false);
        binding.btnLogout.setText("退出中...");

        // 发送退出登录请求
        Call<ApiResponse<Void>> call = apiService.logout();
        call.enqueue(new Callback<ApiResponse<Void>>() {
            @Override
            public void onResponse(Call<ApiResponse<Void>> call, Response<ApiResponse<Void>> response) {
                handleLogoutResponse(response);
            }

            @Override
            public void onFailure(Call<ApiResponse<Void>> call, Throwable t) {
                // 即使网络请求失败，也执行本地退出登录
                handleLogoutResponse(null);
            }
        });
    }

    /**
     * 处理退出登录响应
     */
    private void handleLogoutResponse(Response<ApiResponse<Void>> response) {
        // 清除本地用户信息
        preferenceManager.clearUserInfo();

        // 显示退出成功消息
        Toast.makeText(requireContext(), "已退出登录", Toast.LENGTH_SHORT).show();

        // 跳转到登录页面
        Intent intent = new Intent(requireContext(), LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);

        // 结束当前Activity
        if (getActivity() != null) {
            getActivity().finish();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
