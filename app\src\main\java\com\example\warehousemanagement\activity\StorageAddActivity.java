package com.example.warehousemanagement.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.example.warehousemanagement.R;
import com.example.warehousemanagement.adapter.ScannedTagAdapter;
import com.example.warehousemanagement.databinding.ActivityStorageAddBinding;
import com.example.warehousemanagement.model.ApiResponse;
import com.example.warehousemanagement.model.StorageRequest;
import com.example.warehousemanagement.network.ApiClient;
import com.example.warehousemanagement.network.ApiService;
import com.example.warehousemanagement.rfid.RfidManager;
import com.example.warehousemanagement.utils.DateUtils;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 入库添加Activity
 * 功能：扫描RFID标签并添加入库记录
 */
public class StorageAddActivity extends AppCompatActivity implements RfidManager.RfidDataListener, RfidManager.RfidStatusListener {

    private ActivityStorageAddBinding binding;
    private RfidManager rfidManager;
    private ApiService apiService;
    private ScannedTagAdapter adapter;
    private List<String> scannedTags;
    private Set<String> tagSet; // 用于去重

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityStorageAddBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        initViews();
        initRfid();
        initRecyclerView();
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        apiService = ApiClient.getInstance().getApiService();

        // 设置工具栏
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("添加入库");
        }

        // 设置当前日期
        binding.etStorageDate.setText(DateUtils.getCurrentDate());

        // 设置按钮点击事件
        binding.btnScan.setOnClickListener(v -> toggleScanning());
        binding.btnSave.setOnClickListener(v -> saveStorageRecords());
    }

    /**
     * 初始化RFID
     */
    private void initRfid() {
        rfidManager = RfidManager.getInstance();
        rfidManager.addDataListener(this);
        rfidManager.addStatusListener(this);

        // 更新连接状态
        updateConnectionStatus();
    }

    /**
     * 初始化RecyclerView
     */
    private void initRecyclerView() {
        scannedTags = new ArrayList<>();
        tagSet = new HashSet<>();
        adapter = new ScannedTagAdapter(scannedTags);

        // 设置删除监听器
        adapter.setOnDeleteClickListener(position -> {
            if (position >= 0 && position < scannedTags.size()) {
                String removedTag = scannedTags.remove(position);
                tagSet.remove(removedTag);
                adapter.notifyItemRemoved(position);
                updateTagCount();
            }
        });

        binding.rvScannedTags.setLayoutManager(new LinearLayoutManager(this));
        binding.rvScannedTags.setAdapter(adapter);

        updateTagCount();
    }

    /**
     * 切换扫描状态
     */
    private void toggleScanning() {
        if (!rfidManager.isConnected()) {
            Toast.makeText(this, "RFID设备未连接", Toast.LENGTH_SHORT).show();
            return;
        }

        if (rfidManager.isScanning()) {
            rfidManager.stopScanning();
        } else {
            rfidManager.startScanning();
        }
    }

    /**
     * 保存入库记录
     */
    private void saveStorageRecords() {
        if (scannedTags.isEmpty()) {
            Toast.makeText(this, "请先扫描标签", Toast.LENGTH_SHORT).show();
            return;
        }

        // 显示加载状态
        binding.btnSave.setEnabled(false);
        binding.btnSave.setText("保存中...");

        // 创建入库请求
        List<StorageRequest.StorageTag> tags = new ArrayList<>();
        String currentDate = DateUtils.getCurrentDate();
        String remark1 = binding.etRemark1.getText().toString().trim();
        String remark2 = binding.etRemark2.getText().toString().trim();

        for (String tagId : scannedTags) {
            StorageRequest.StorageTag tag = new StorageRequest.StorageTag(tagId, currentDate);
            if (!remark1.isEmpty()) {
                tag.setRemark1(remark1);
            }
            if (!remark2.isEmpty()) {
                tag.setRemark2(remark2);
            }
            tags.add(tag);
        }

        StorageRequest request = new StorageRequest(tags);

        // 发送网络请求
        Call<ApiResponse<Void>> call = apiService.addStorageRecords(request);
        call.enqueue(new Callback<ApiResponse<Void>>() {
            @Override
            public void onResponse(Call<ApiResponse<Void>> call, Response<ApiResponse<Void>> response) {
                binding.btnSave.setEnabled(true);
                binding.btnSave.setText("保存");

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<Void> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        Toast.makeText(StorageAddActivity.this, "入库成功", Toast.LENGTH_SHORT).show();
                        finish();
                    } else {
                        Toast.makeText(StorageAddActivity.this, apiResponse.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                } else {
                    Toast.makeText(StorageAddActivity.this, "入库失败，请检查网络连接", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<Void>> call, Throwable t) {
                binding.btnSave.setEnabled(true);
                binding.btnSave.setText("保存");
                Toast.makeText(StorageAddActivity.this, "网络错误：" + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * 更新标签数量显示
     */
    private void updateTagCount() {
        binding.tvTagCount.setText("已扫描标签: " + scannedTags.size() + "个");
    }

    /**
     * 更新连接状态
     */
    private void updateConnectionStatus() {
        if (rfidManager.isConnected()) {
            binding.tvRfidStatus.setText("设备状态: 已连接");
            binding.btnScan.setEnabled(true);
        } else {
            binding.tvRfidStatus.setText("设备状态: 未连接");
            binding.btnScan.setEnabled(false);
        }
    }

    /**
     * 更新扫描状态
     */
    private void updateScanningStatus() {
        if (rfidManager.isScanning()) {
            binding.btnScan.setText("停止扫描");
        } else {
            binding.btnScan.setText("开始扫描");
        }
    }

    // ========== RFID监听器实现 ==========

    @Override
    public void onRfidDataReceived(List<String> tagIds) {
        runOnUiThread(() -> {
            for (String tagId : tagIds) {
                if (!tagSet.contains(tagId)) {
                    tagSet.add(tagId);
                    scannedTags.add(tagId);
                    adapter.notifyItemInserted(scannedTags.size() - 1);
                }
            }
            updateTagCount();
        });
    }

    @Override
    public void onConnectionStatusChanged(boolean connected) {
        runOnUiThread(this::updateConnectionStatus);
    }

    @Override
    public void onScanningStatusChanged(boolean scanning) {
        runOnUiThread(this::updateScanningStatus);
    }

    @Override
    public void onError(String error) {
        runOnUiThread(() -> Toast.makeText(this, error, Toast.LENGTH_SHORT).show());
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateConnectionStatus();
        updateScanningStatus();
    }

    @Override
    protected void onPause() {
        super.onPause();
        // 停止扫描
        if (rfidManager.isScanning()) {
            rfidManager.stopScanning();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 移除监听器
        rfidManager.removeDataListener(this);
        rfidManager.removeStatusListener(this);
        binding = null;
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}
