package com.example.warehousemanagement.model;

/**
 * 通用API响应模型
 */
public class ApiResponse<T> {
    private int code;
    private String message;
    private T data;
    
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    /**
     * 判断请求是否成功
     */
    public boolean isSuccess() {
        return code == 200;
    }
}
