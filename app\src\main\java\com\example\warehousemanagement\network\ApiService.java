package com.example.warehousemanagement.network;

import com.example.warehousemanagement.model.LoginRequest;
import com.example.warehousemanagement.model.LoginResponse;
import com.example.warehousemanagement.model.RfidTag;
import com.example.warehousemanagement.model.ApiResponse;
import com.example.warehousemanagement.model.StorageRequest;
import com.example.warehousemanagement.model.ShippingRequest;
import com.example.warehousemanagement.model.RecoveryRequest;
import com.example.warehousemanagement.model.Settings;
import retrofit2.Call;
import retrofit2.http.*;
import java.util.List;

/**
 * API服务接口
 * 定义所有网络请求接口
 */
public interface ApiService {
    
    // ========== 用户认证接口 ==========
    
    /**
     * 用户登录
     */
    @POST("api/auth/login")
    Call<LoginResponse> login(@Body LoginRequest request);
    
    /**
     * 用户退出登录
     */
    @POST("api/auth/logout")
    Call<ApiResponse<Void>> logout();
    
    // ========== 标签管理接口 ==========
    
    /**
     * 查询入库记录
     */
    @GET("api/tags/storage")
    Call<ApiResponse<List<RfidTag>>> getStorageRecords(
            @Query("storage_date_start") String startDate,
            @Query("storage_date_end") String endDate,
            @Query("tag_type") String tagType,
            @Query("remark") String remark
    );
    
    /**
     * 添加入库记录
     */
    @POST("api/tags/storage")
    Call<ApiResponse<Void>> addStorageRecords(@Body StorageRequest request);
    
    /**
     * 查询发货记录
     */
    @GET("api/tags/shipping")
    Call<ApiResponse<List<RfidTag>>> getShippingRecords(
            @Query("shipping_date_start") String startDate,
            @Query("shipping_date_end") String endDate,
            @Query("customer_name") String customerName,
            @Query("tag_type") String tagType,
            @Query("remark") String remark
    );
    
    /**
     * 添加发货记录
     */
    @POST("api/tags/shipping")
    Call<ApiResponse<Void>> addShippingRecords(@Body ShippingRequest request);
    
    /**
     * 查询回收记录
     */
    @GET("api/tags/recovery")
    Call<ApiResponse<List<RfidTag>>> getRecoveryRecords(
            @Query("recovery_date_start") String startDate,
            @Query("recovery_date_end") String endDate,
            @Query("customer_name") String customerName,
            @Query("tag_type") String tagType,
            @Query("remark") String remark
    );
    
    /**
     * 添加回收记录
     */
    @POST("api/tags/recovery")
    Call<ApiResponse<Void>> addRecoveryRecords(@Body RecoveryRequest request);
    
    /**
     * 查询标签详细信息
     */
    @GET("api/tags/info/{tag_id}")
    Call<ApiResponse<RfidTag>> getTagInfo(@Path("tag_id") String tagId);
    
    // ========== 系统设置接口 ==========
    
    /**
     * 获取系统设置
     */
    @GET("api/settings")
    Call<ApiResponse<Settings>> getSettings();
    
    /**
     * 更新系统设置
     */
    @POST("api/settings")
    Call<ApiResponse<Void>> updateSettings(@Body Settings settings);
}
