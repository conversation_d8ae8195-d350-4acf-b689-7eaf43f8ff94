// Generated by view binder compiler. Do not edit!
package com.example.warehousemanagement.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.warehousemanagement.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemScannedTagBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView ivDelete;

  @NonNull
  public final TextView tvScanTime;

  @NonNull
  public final TextView tvTagId;

  private ItemScannedTagBinding(@NonNull LinearLayout rootView, @NonNull ImageView ivDelete,
      @NonNull TextView tvScanTime, @NonNull TextView tvTagId) {
    this.rootView = rootView;
    this.ivDelete = ivDelete;
    this.tvScanTime = tvScanTime;
    this.tvTagId = tvTagId;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemScannedTagBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemScannedTagBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_scanned_tag, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemScannedTagBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_delete;
      ImageView ivDelete = ViewBindings.findChildViewById(rootView, id);
      if (ivDelete == null) {
        break missingId;
      }

      id = R.id.tv_scan_time;
      TextView tvScanTime = ViewBindings.findChildViewById(rootView, id);
      if (tvScanTime == null) {
        break missingId;
      }

      id = R.id.tv_tag_id;
      TextView tvTagId = ViewBindings.findChildViewById(rootView, id);
      if (tvTagId == null) {
        break missingId;
      }

      return new ItemScannedTagBinding((LinearLayout) rootView, ivDelete, tvScanTime, tvTagId);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
