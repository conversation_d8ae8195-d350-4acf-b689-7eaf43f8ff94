1.用户登陆界面
1.1用户输入帐号和密码登陆，错误进行提示
1.2登陆成功后自动开启RFID设备，退出程序时关闭
2.内容页
2.1所有内容页面统一在下方有个tabbar，按钮分别为入库、发货、回收、我的，分别对应不同页面
2.2所有RFID标签扫描后会返回一串文本字符串，字符串内容由|进行分割，转换成字符串数组后，不同数组下标表示不同内容，其中数组下标为0的为标签ID，格式为hky+6位数字和字母组成（如a00001或000001）+x或d，x为箱子，d为袋子；数组下标为1的内容暂时为“航科院（北京）科技发展有限公司”；数组下标为2的内容为产品类型，跟据标签ID最后的x或d分别为“防火阻燃箱" 或 “防火阻燃袋”；数组下标为3的内容暂时为“LIB-300”；数组下标为4的内容为货品联系人电话，如“13116134567”
3.产品入库
3.1查询当前已入库的的所有记录，列表形式，查询条件可跟据入库日期、备注字段、物品类型（x或d）箱子或袋子进行查询
3.2添加新入库记录
3.2.1添加界面进入后列表形式显示已扫描进来的RFID标签，列表中显示标签ID、入库日期，可用操作为删除，扫描后首先追加进LIST，删除也只对LIST进行操作，相同标签ID只可录入一次，确保唯一，当所有需要的标签均扫描完毕后点击保存按钮进入入库操作，保存时需向数据库中保存以下字段“标签ID、状态（入库、发货、回收）、入库日期、发货日期、客户名称、回收日期、备注1、备注2、备注3、备注4、备注5”
4.产品发货
4.1查询当前已发货的所有记录，列表形式，查询条件可跟据发货日期、客户名称、备注字段、物品类型（x或d）箱子或袋子进行查询
4.2添加新发货记录
4.2.1添加界面进入后列表形式显示已扫描进来的RFID标签，列表中显示标签ID、入库日期，可用操作为删除，扫描后首先追加进LIST，删除也只对LIST进行操作，相同标签ID只可录入一次，确保唯一，当所有需要的标签均扫描完毕后点击保存按钮进入发货操作，保存时需向数据库中跟据标签ID查询到记录并修改以下字段“状态（入库、发货、回收）、发货日期、客户名称、备注1、备注2、备注3、备注4、备注5”
5产品回收
5.1查询当前已回收的所有记录，列表形式，查询条件可跟据回收日期、客户名称、备注字段、物品类型（x或d）箱子或袋子进行查询
5.2添加新回收记录
5.2.1添加界面进入后列表形式显示已扫描进来的RFID标签，列表中显示标签ID、客户、发货日期、保质期（可在设置里配置时长，以天为单位，袋子和箱子分别设置不同的保质期）、已使用天数（当前日期-入库日期）、是否需要回收（保质期-已使用天数如果小于回收天数的显示需要回收并显示可用天数，大于回收天数显示不需要回收并显示可用天数），可用操作为删除，扫描后首先追加进LIST，删除也只对LIST进行操作，相同标签ID只可录入一次，确保唯一，当所有需要的标签均扫描完毕后点击保存按钮进入回收操作，保存时需向数据库中跟据标签ID查询到记录并修改以下字段“状态（入库、发货、回收）、回收日期、备注1、备注2、备注3、备注4、备注5”
6.我的
6.1我的界面里显示用户名和退出登陆，点退出后回到登陆页
6.2我的界面里需要进行如下设置项：1、消息通知复选框，勾选后可以收到系统推送的消息；2、消息通知设置（保质期区分箱子和袋子、回收天数）；3、扫描识别距离设置（1-3米）
