package com.example.warehousemanagement.adapter;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.warehousemanagement.databinding.ItemScannedTagBinding;
import com.example.warehousemanagement.utils.DateUtils;
import java.util.List;

/**
 * 已扫描标签适配器
 * 用于显示扫描到的RFID标签列表
 */
public class ScannedTagAdapter extends RecyclerView.Adapter<ScannedTagAdapter.ScannedTagViewHolder> {

    private List<String> tagList;
    private OnDeleteClickListener onDeleteClickListener;

    public interface OnDeleteClickListener {
        void onDeleteClick(int position);
    }

    public ScannedTagAdapter(List<String> tagList) {
        this.tagList = tagList;
    }

    public void setOnDeleteClickListener(OnDeleteClickListener listener) {
        this.onDeleteClickListener = listener;
    }

    @NonNull
    @Override
    public ScannedTagViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemScannedTagBinding binding = ItemScannedTagBinding.inflate(
            LayoutInflater.from(parent.getContext()), parent, false);
        return new ScannedTagViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull ScannedTagViewHolder holder, int position) {
        String tagId = tagList.get(position);
        holder.bind(tagId, position);
    }

    @Override
    public int getItemCount() {
        return tagList.size();
    }

    class ScannedTagViewHolder extends RecyclerView.ViewHolder {
        private ItemScannedTagBinding binding;

        public ScannedTagViewHolder(ItemScannedTagBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }

        public void bind(String tagId, int position) {
            // 显示标签ID
            binding.tvTagId.setText(tagId);

            // 显示扫描时间
            binding.tvScanTime.setText("扫描时间: " + DateUtils.getCurrentDateTime());

            // 设置删除按钮点击事件
            binding.ivDelete.setOnClickListener(v -> {
                if (onDeleteClickListener != null) {
                    onDeleteClickListener.onDeleteClick(position);
                }
            });
        }
    }
}
