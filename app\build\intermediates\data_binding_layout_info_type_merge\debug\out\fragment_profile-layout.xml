<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_profile" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\fragment_profile.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_profile_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="150" endOffset="14"/></Target><Target id="@+id/iv_avatar" view="ImageView"><Expressions/><location startLine="21" startOffset="12" endLine="29" endOffset="50"/></Target><Target id="@+id/tv_username" view="TextView"><Expressions/><location startLine="38" startOffset="16" endLine="42" endOffset="55"/></Target><Target id="@+id/tv_role" view="TextView"><Expressions/><location startLine="44" startOffset="16" endLine="48" endOffset="63"/></Target><Target id="@+id/ll_settings" view="LinearLayout"><Expressions/><location startLine="68" startOffset="12" endLine="97" endOffset="26"/></Target><Target id="@+id/ll_about" view="LinearLayout"><Expressions/><location startLine="107" startOffset="12" endLine="136" endOffset="26"/></Target><Target id="@+id/btn_logout" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="143" startOffset="4" endLine="148" endOffset="42"/></Target></Targets></Layout>