<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background">

    <!-- 用户信息卡片 -->
    <com.google.android.material.card.MaterialCardView
        style="@style/CardStyle"
        android:layout_margin="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <!-- 用户头像 -->
            <ImageView
                android:id="@+id/iv_avatar"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_person"
                android:background="@drawable/circle_background"
                android:padding="16dp"
                android:scaleType="centerInside" />

            <!-- 用户信息 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_username"
                    style="@style/TitleText"
                    android:text="用户名"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_role"
                    style="@style/BodyText"
                    android:text="管理员"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 功能菜单 -->
    <com.google.android.material.card.MaterialCardView
        style="@style/CardStyle"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 系统设置 -->
            <LinearLayout
                android:id="@+id/ll_settings"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="16dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_settings"
                    android:tint="@color/primary" />

                <TextView
                    style="@style/BodyText"
                    android:text="@string/system_settings"
                    android:layout_weight="1" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_arrow_forward"
                    android:tint="@color/text_secondary" />

            </LinearLayout>

            <!-- 分割线 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginHorizontal="16dp"
                android:background="@color/divider" />

            <!-- 关于 -->
            <LinearLayout
                android:id="@+id/ll_about"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="16dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_info"
                    android:tint="@color/primary" />

                <TextView
                    style="@style/BodyText"
                    android:text="@string/about"
                    android:layout_weight="1" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_arrow_forward"
                    android:tint="@color/text_secondary" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 退出登录按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_logout"
        style="@style/SecondaryButton"
        android:text="@string/logout"
        android:layout_margin="16dp"
        android:textColor="@color/error" />

</LinearLayout>