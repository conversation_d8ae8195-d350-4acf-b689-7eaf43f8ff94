<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_settings" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\activity_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_settings_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="194" endOffset="14"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="9" startOffset="4" endLine="16" endOffset="54"/></Target><Target id="@+id/et_server_url" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="49" startOffset="24" endLine="53" endOffset="57"/></Target><Target id="@+id/et_timeout" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="63" startOffset="24" endLine="67" endOffset="56"/></Target><Target id="@+id/switch_auto_connect" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="104" startOffset="24" endLine="107" endOffset="66"/></Target><Target id="@+id/slider_scan_power" view="com.google.android.material.slider.Slider"><Expressions/><location startLine="117" startOffset="20" endLine="124" endOffset="43"/></Target><Target id="@+id/switch_auto_sync" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="158" startOffset="24" endLine="161" endOffset="66"/></Target><Target id="@+id/et_sync_interval" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="171" startOffset="24" endLine="175" endOffset="56"/></Target><Target id="@+id/btn_save_settings" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="188" startOffset="4" endLine="192" endOffset="38"/></Target></Targets></Layout>