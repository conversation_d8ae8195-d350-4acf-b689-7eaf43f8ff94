package densowave.com.DensoScannerSDK_demo;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

public class TabAdapter extends FragmentStateAdapter {
    private static final int TAB_COUNT = 2;
    private static final int TAB_SCAN_RESULTS = 0;
    private static final int TAB_ERROR_DETAILS = 1;

    private final ScanResultsFragment scanResultsFragment;
    private final ErrorDetailsFragment errorDetailsFragment;

    public TabAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
        // 创建Fragment实例
        scanResultsFragment = new ScanResultsFragment();
        errorDetailsFragment = new ErrorDetailsFragment();
        
        // 打印调试信息
        System.out.println("TabAdapter已创建，Fragment已初始化");
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        System.out.println("创建Fragment，位置: " + position);
        switch (position) {
            case TAB_SCAN_RESULTS:
                return scanResultsFragment;
            case TAB_ERROR_DETAILS:
                return errorDetailsFragment;
            default:
                return scanResultsFragment;
        }
    }

    @Override
    public int getItemCount() {
        return TAB_COUNT;
    }

    public ScanResultsFragment getScanResultsFragment() {
        return scanResultsFragment;
    }

    public ErrorDetailsFragment getErrorDetailsFragment() {
        return errorDetailsFragment;
    }
}
