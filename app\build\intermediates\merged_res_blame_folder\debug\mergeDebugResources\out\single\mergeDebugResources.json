[{"merged": "com.example.warehousemanagement.app-debug-64:/layout_activity_storage_add.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/activity_storage_add.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/drawable_ic_login.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/drawable/ic_login.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/drawable_ic_shipping.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/drawable/ic_shipping.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/drawable_ic_arrow_back.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/drawable/ic_arrow_back.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/drawable_ic_person.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/drawable/ic_person.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/layout_activity_main.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/activity_main.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/mipmap-hdpi_ic_launcher_round.png.flat", "source": "com.example.warehousemanagement.app-main-66:/mipmap-hdpi/ic_launcher_round.png"}, {"merged": "com.example.warehousemanagement.app-debug-64:/drawable_ic_info.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/drawable/ic_info.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/layout_fragment_scan_results.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/fragment_scan_results.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/drawable_ic_tag.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/drawable/ic_tag.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/layout_activity_settings.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/activity_settings.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/layout_activity_simple_rfid.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/activity_simple_rfid.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/mipmap-mdpi_ic_launcher_round.png.flat", "source": "com.example.warehousemanagement.app-main-66:/mipmap-mdpi/ic_launcher_round.png"}, {"merged": "com.example.warehousemanagement.app-debug-64:/menu_bottom_navigation_menu.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/menu/bottom_navigation_menu.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/drawable_circle_background.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/drawable/circle_background.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/layout_activity_shipping_add.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/activity_shipping_add.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/drawable_status_background.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/drawable/status_background.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/drawable_ic_profile.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/drawable/ic_profile.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/drawable_ic_delete.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/drawable/ic_delete.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/layout_fragment_shipping.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/fragment_shipping.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/layout_fragment_error_details.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/fragment_error_details.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/layout_fragment_recovery.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/fragment_recovery.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.example.warehousemanagement.app-main-66:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.example.warehousemanagement.app-debug-64:/drawable_ic_settings.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/drawable/ic_settings.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/drawable_ic_recycling.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/drawable/ic_recycling.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/layout_activity_recovery_add.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/activity_recovery_add.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.example.warehousemanagement.app-main-66:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.example.warehousemanagement.app-debug-64:/layout_item_record.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/item_record.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/layout_fragment_profile.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/fragment_profile.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/layout_fragment_storage.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/fragment_storage.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/drawable_ic_inventory.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/drawable/ic_inventory.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/layout_item_scanned_tag.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/item_scanned_tag.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/layout_activity_login.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/layout/activity_login.xml"}, {"merged": "com.example.warehousemanagement.app-debug-64:/drawable_ic_arrow_forward.xml.flat", "source": "com.example.warehousemanagement.app-main-66:/drawable/ic_arrow_forward.xml"}]