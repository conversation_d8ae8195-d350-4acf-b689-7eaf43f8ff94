<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.warehousemanagement">

    <!-- 蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <!-- Android 12+ 蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <!-- 位置权限（蓝牙扫描需要） -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <!-- 本地设备访问权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- 蓝牙设备特性声明 -->
    <uses-feature android:name="android.hardware.bluetooth" android:required="true" />
    <uses-feature android:name="android.hardware.bluetooth_le" android:required="true" />

    <application
        android:name=".WarehouseApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.WarehouseManagement">
        
        <!-- 登录Activity -->
        <activity
            android:name=".LoginActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <activity
            android:name=".MainActivity"
            android:exported="false" />
        
        <!-- 添加记录的Activity -->
        <activity
            android:name=".activities.AddStorageActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity" />
        
        <activity
            android:name=".activities.AddShippingActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity" />
        
        <activity
            android:name=".activities.AddRecoveryActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity" />
        
        <!-- 设置Activity -->
        <activity
            android:name=".activities.SettingsActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity" />
        
        <!-- 数据同步服务 -->
        <service
            android:name=".services.DataSyncService"
            android:enabled="true"
            android:exported="false" />
    </application>

</manifest>
