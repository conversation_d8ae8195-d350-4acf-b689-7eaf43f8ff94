package com.example.warehousemanagement.activity;

import android.os.Bundle;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.example.warehousemanagement.R;
import com.example.warehousemanagement.databinding.ActivitySettingsBinding;
import com.example.warehousemanagement.model.ApiResponse;
import com.example.warehousemanagement.model.Settings;
import com.example.warehousemanagement.network.ApiClient;
import com.example.warehousemanagement.network.ApiService;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 设置Activity
 * 功能：系统设置管理
 */
public class SettingsActivity extends AppCompatActivity {

    private ActivitySettingsBinding binding;
    private ApiService apiService;
    private Settings currentSettings;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivitySettingsBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        initViews();
        loadSettings();
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        apiService = ApiClient.getInstance().getApiService();

        // 设置工具栏
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("系统设置");
        }

        // 设置按钮点击事件
        binding.btnSaveSettings.setOnClickListener(v -> saveSettings());
    }

    /**
     * 加载设置
     */
    private void loadSettings() {
        Call<ApiResponse<Settings>> call = apiService.getSettings();
        call.enqueue(new Callback<ApiResponse<Settings>>() {
            @Override
            public void onResponse(Call<ApiResponse<Settings>> call, Response<ApiResponse<Settings>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<Settings> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        currentSettings = apiResponse.getData();
                        displaySettings(currentSettings);
                    } else {
                        showError(apiResponse.getMessage());
                    }
                } else {
                    showError("加载设置失败，请检查网络连接");
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<Settings>> call, Throwable t) {
                showError("网络错误：" + t.getMessage());
            }
        });
    }

    /**
     * 显示设置
     */
    private void displaySettings(Settings settings) {
        binding.etServerUrl.setText(settings.getServerUrl() != null ? settings.getServerUrl() : "");
        binding.etTimeout.setText(String.valueOf(settings.getTimeout()));
        binding.switchAutoConnect.setChecked(settings.isAutoConnect());
        binding.sliderScanPower.setValue(settings.getScanPower());
        binding.switchAutoSync.setChecked(settings.isAutoSync());
        binding.etSyncInterval.setText(String.valueOf(settings.getSyncInterval()));
    }

    /**
     * 保存设置
     */
    private void saveSettings() {
        try {
            // 获取输入值
            String serverUrl = binding.etServerUrl.getText() != null ?
                binding.etServerUrl.getText().toString().trim() : "";
            String timeoutStr = binding.etTimeout.getText() != null ?
                binding.etTimeout.getText().toString().trim() : "0";
            String syncIntervalStr = binding.etSyncInterval.getText() != null ?
                binding.etSyncInterval.getText().toString().trim() : "0";

            int timeout = Integer.parseInt(timeoutStr);
            boolean autoConnect = binding.switchAutoConnect.isChecked();
            int scanPower = (int) binding.sliderScanPower.getValue();
            boolean autoSync = binding.switchAutoSync.isChecked();
            int syncInterval = Integer.parseInt(syncIntervalStr);

            // 验证输入
            if (serverUrl.isEmpty()) {
                binding.etServerUrl.setError("服务器地址不能为空");
                return;
            }

            if (timeout <= 0) {
                binding.etTimeout.setError("连接超时必须大于0");
                return;
            }

            if (syncInterval <= 0) {
                binding.etSyncInterval.setError("同步间隔必须大于0");
                return;
            }

            // 创建设置对象
            Settings settings = new Settings();
            settings.setServerUrl(serverUrl);
            settings.setTimeout(timeout);
            settings.setAutoConnect(autoConnect);
            settings.setScanPower(scanPower);
            settings.setAutoSync(autoSync);
            settings.setSyncInterval(syncInterval);

            // 显示加载状态
            binding.btnSaveSettings.setEnabled(false);
            binding.btnSaveSettings.setText("保存中...");

            // 发送网络请求
            Call<ApiResponse<Void>> call = apiService.updateSettings(settings);
            call.enqueue(new Callback<ApiResponse<Void>>() {
                @Override
                public void onResponse(Call<ApiResponse<Void>> call, Response<ApiResponse<Void>> response) {
                    binding.btnSaveSettings.setEnabled(true);
                    binding.btnSaveSettings.setText("保存设置");

                    if (response.isSuccessful() && response.body() != null) {
                        ApiResponse<Void> apiResponse = response.body();
                        if (apiResponse.isSuccess()) {
                            Toast.makeText(SettingsActivity.this, "设置保存成功", Toast.LENGTH_SHORT).show();
                            currentSettings = settings;
                        } else {
                            showError(apiResponse.getMessage());
                        }
                    } else {
                        showError("保存设置失败，请检查网络连接");
                    }
                }

                @Override
                public void onFailure(Call<ApiResponse<Void>> call, Throwable t) {
                    binding.btnSaveSettings.setEnabled(true);
                    binding.btnSaveSettings.setText("保存设置");
                    showError("网络错误：" + t.getMessage());
                }
            });

        } catch (NumberFormatException e) {
            showError("请输入有效的数字");
        }
    }

    /**
     * 显示错误信息
     */
    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}
