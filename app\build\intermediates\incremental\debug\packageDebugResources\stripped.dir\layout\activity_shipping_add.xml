<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background">

    <!-- 顶部工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:elevation="4dp"
        app:title="@string/shipping_add"
        app:titleTextColor="@color/text_white"
        app:navigationIcon="?attr/homeAsUpIndicator" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 扫描区域 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/CardStyle"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        style="@style/TitleTextStyle"
                        android:text="RFID扫描"
                        android:layout_marginBottom="16dp" />

                    <!-- RFID连接状态 -->
                    <TextView
                        android:id="@+id/tv_rfid_status"
                        style="@style/BodyTextStyle"
                        android:text="@string/rfid_disconnected"
                        android:textColor="@color/error"
                        android:layout_marginBottom="12dp" />

                    <!-- 扫描按钮 -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_scan"
                        style="@style/PrimaryButton"
                        android:text="@string/rfid_scan_start"
                        android:layout_height="48dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 已扫描标签列表 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/CardStyle"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            style="@style/TitleTextStyle"
                            android:text="已扫描标签"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/tv_tag_count"
                            style="@style/SubtitleTextStyle"
                            android:text="共0个" />

                    </LinearLayout>

                    <!-- 标签列表 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_scanned_tags"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                    <!-- 空状态提示 -->
                    <TextView
                        android:id="@+id/tv_empty_tags"
                        style="@style/BodyTextStyle"
                        android:text="暂无扫描到的标签"
                        android:textColor="@color/text_hint"
                        android:gravity="center"
                        android:layout_width="match_parent"
                        android:layout_height="100dp"
                        android:visibility="visible" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 发货信息 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/CardStyle"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        style="@style/TitleTextStyle"
                        android:text="发货信息"
                        android:layout_marginBottom="16dp" />

                    <!-- 客户名称 -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/EditTextStyle"
                        android:hint="@string/customer_name">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_customer_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 发货日期 -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/EditTextStyle"
                        android:hint="@string/shipping_date">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_shipping_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:focusable="false"
                            android:clickable="true" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 备注1 -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/EditTextStyle"
                        android:hint="备注1">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_remark1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 备注2 -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/EditTextStyle"
                        android:hint="备注2">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_remark2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="0dp" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

    <!-- 底部按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/surface"
        android:elevation="4dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            style="@style/SecondaryButton"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="@string/cancel" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            style="@style/PrimaryButton"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="@string/save" />

    </LinearLayout>

</LinearLayout>