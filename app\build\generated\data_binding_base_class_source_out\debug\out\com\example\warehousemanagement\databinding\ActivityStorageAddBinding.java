// Generated by view binder compiler. Do not edit!
package com.example.warehousemanagement.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.warehousemanagement.R;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityStorageAddBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnCancel;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final MaterialButton btnScan;

  @NonNull
  public final TextInputEditText etRemark1;

  @NonNull
  public final TextInputEditText etRemark2;

  @NonNull
  public final TextInputEditText etStorageDate;

  @NonNull
  public final RecyclerView rvScannedTags;

  @NonNull
  public final MaterialToolbar toolbar;

  @NonNull
  public final TextView tvEmptyTags;

  @NonNull
  public final TextView tvRfidStatus;

  @NonNull
  public final TextView tvTagCount;

  private ActivityStorageAddBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton btnCancel, @NonNull MaterialButton btnSave,
      @NonNull MaterialButton btnScan, @NonNull TextInputEditText etRemark1,
      @NonNull TextInputEditText etRemark2, @NonNull TextInputEditText etStorageDate,
      @NonNull RecyclerView rvScannedTags, @NonNull MaterialToolbar toolbar,
      @NonNull TextView tvEmptyTags, @NonNull TextView tvRfidStatus, @NonNull TextView tvTagCount) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnSave = btnSave;
    this.btnScan = btnScan;
    this.etRemark1 = etRemark1;
    this.etRemark2 = etRemark2;
    this.etStorageDate = etStorageDate;
    this.rvScannedTags = rvScannedTags;
    this.toolbar = toolbar;
    this.tvEmptyTags = tvEmptyTags;
    this.tvRfidStatus = tvRfidStatus;
    this.tvTagCount = tvTagCount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityStorageAddBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityStorageAddBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_storage_add, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityStorageAddBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel;
      MaterialButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_save;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.btn_scan;
      MaterialButton btnScan = ViewBindings.findChildViewById(rootView, id);
      if (btnScan == null) {
        break missingId;
      }

      id = R.id.et_remark1;
      TextInputEditText etRemark1 = ViewBindings.findChildViewById(rootView, id);
      if (etRemark1 == null) {
        break missingId;
      }

      id = R.id.et_remark2;
      TextInputEditText etRemark2 = ViewBindings.findChildViewById(rootView, id);
      if (etRemark2 == null) {
        break missingId;
      }

      id = R.id.et_storage_date;
      TextInputEditText etStorageDate = ViewBindings.findChildViewById(rootView, id);
      if (etStorageDate == null) {
        break missingId;
      }

      id = R.id.rv_scanned_tags;
      RecyclerView rvScannedTags = ViewBindings.findChildViewById(rootView, id);
      if (rvScannedTags == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_empty_tags;
      TextView tvEmptyTags = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyTags == null) {
        break missingId;
      }

      id = R.id.tv_rfid_status;
      TextView tvRfidStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvRfidStatus == null) {
        break missingId;
      }

      id = R.id.tv_tag_count;
      TextView tvTagCount = ViewBindings.findChildViewById(rootView, id);
      if (tvTagCount == null) {
        break missingId;
      }

      return new ActivityStorageAddBinding((LinearLayout) rootView, btnCancel, btnSave, btnScan,
          etRemark1, etRemark2, etStorageDate, rvScannedTags, toolbar, tvEmptyTags, tvRfidStatus,
          tvTagCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
