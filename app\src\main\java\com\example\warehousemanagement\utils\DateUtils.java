package com.example.warehousemanagement.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 日期工具类
 */
public class DateUtils {
    
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    
    private static final SimpleDateFormat dateFormatter = new SimpleDateFormat(DATE_FORMAT, Locale.getDefault());
    private static final SimpleDateFormat datetimeFormatter = new SimpleDateFormat(DATETIME_FORMAT, Locale.getDefault());
    
    /**
     * 格式化日期为字符串
     */
    public static String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        return dateFormatter.format(date);
    }
    
    /**
     * 格式化日期时间为字符串
     */
    public static String formatDateTime(Date date) {
        if (date == null) {
            return "";
        }
        return datetimeFormatter.format(date);
    }
    
    /**
     * 解析日期字符串
     */
    public static Date parseDate(String dateString) {
        try {
            return dateFormatter.parse(dateString);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 解析日期时间字符串
     */
    public static Date parseDateTime(String dateTimeString) {
        try {
            return datetimeFormatter.parse(dateTimeString);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 获取当前日期字符串
     */
    public static String getCurrentDate() {
        return formatDate(new Date());
    }
    
    /**
     * 获取当前日期时间字符串
     */
    public static String getCurrentDateTime() {
        return formatDateTime(new Date());
    }
    
    /**
     * 计算两个日期之间的天数差
     */
    public static int daysBetween(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return 0;
        }
        
        long diffInMillies = endDate.getTime() - startDate.getTime();
        return (int) (diffInMillies / (1000 * 60 * 60 * 24));
    }
    
    /**
     * 计算从指定日期到现在的天数
     */
    public static int daysFromNow(Date date) {
        if (date == null) {
            return 0;
        }
        return daysBetween(date, new Date());
    }
}
