// Generated by view binder compiler. Do not edit!
package com.example.warehousemanagement.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.warehousemanagement.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemRecordBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final LinearLayout llCustomer;

  @NonNull
  public final LinearLayout llRemark1;

  @NonNull
  public final LinearLayout llRemark2;

  @NonNull
  public final TextView tvCustomer;

  @NonNull
  public final TextView tvDate;

  @NonNull
  public final TextView tvRecordId;

  @NonNull
  public final TextView tvRemark1;

  @NonNull
  public final TextView tvRemark2;

  @NonNull
  public final TextView tvStatus;

  @NonNull
  public final TextView tvTagCount;

  private ItemRecordBinding(@NonNull MaterialCardView rootView, @NonNull LinearLayout llCustomer,
      @NonNull LinearLayout llRemark1, @NonNull LinearLayout llRemark2,
      @NonNull TextView tvCustomer, @NonNull TextView tvDate, @NonNull TextView tvRecordId,
      @NonNull TextView tvRemark1, @NonNull TextView tvRemark2, @NonNull TextView tvStatus,
      @NonNull TextView tvTagCount) {
    this.rootView = rootView;
    this.llCustomer = llCustomer;
    this.llRemark1 = llRemark1;
    this.llRemark2 = llRemark2;
    this.tvCustomer = tvCustomer;
    this.tvDate = tvDate;
    this.tvRecordId = tvRecordId;
    this.tvRemark1 = tvRemark1;
    this.tvRemark2 = tvRemark2;
    this.tvStatus = tvStatus;
    this.tvTagCount = tvTagCount;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemRecordBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemRecordBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_record, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemRecordBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ll_customer;
      LinearLayout llCustomer = ViewBindings.findChildViewById(rootView, id);
      if (llCustomer == null) {
        break missingId;
      }

      id = R.id.ll_remark1;
      LinearLayout llRemark1 = ViewBindings.findChildViewById(rootView, id);
      if (llRemark1 == null) {
        break missingId;
      }

      id = R.id.ll_remark2;
      LinearLayout llRemark2 = ViewBindings.findChildViewById(rootView, id);
      if (llRemark2 == null) {
        break missingId;
      }

      id = R.id.tv_customer;
      TextView tvCustomer = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomer == null) {
        break missingId;
      }

      id = R.id.tv_date;
      TextView tvDate = ViewBindings.findChildViewById(rootView, id);
      if (tvDate == null) {
        break missingId;
      }

      id = R.id.tv_record_id;
      TextView tvRecordId = ViewBindings.findChildViewById(rootView, id);
      if (tvRecordId == null) {
        break missingId;
      }

      id = R.id.tv_remark1;
      TextView tvRemark1 = ViewBindings.findChildViewById(rootView, id);
      if (tvRemark1 == null) {
        break missingId;
      }

      id = R.id.tv_remark2;
      TextView tvRemark2 = ViewBindings.findChildViewById(rootView, id);
      if (tvRemark2 == null) {
        break missingId;
      }

      id = R.id.tv_status;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      id = R.id.tv_tag_count;
      TextView tvTagCount = ViewBindings.findChildViewById(rootView, id);
      if (tvTagCount == null) {
        break missingId;
      }

      return new ItemRecordBinding((MaterialCardView) rootView, llCustomer, llRemark1, llRemark2,
          tvCustomer, tvDate, tvRecordId, tvRemark1, tvRemark2, tvStatus, tvTagCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
