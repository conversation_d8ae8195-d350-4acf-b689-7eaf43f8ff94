<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="12dp"
    android:background="?attr/selectableItemBackground">

    <!-- 标签图标 -->
    <ImageView
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="12dp"
        android:src="@drawable/ic_tag"
        android:tint="@color/primary" />

    <!-- 标签信息 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- 标签ID -->
        <TextView
            android:id="@+id/tv_tag_id"
            style="@style/BodyText"
            android:text="标签ID"
            android:textColor="@color/text_primary" />

        <!-- 扫描时间 -->
        <TextView
            android:id="@+id/tv_scan_time"
            style="@style/BodyText"
            android:text="扫描时间"
            android:textColor="@color/text_secondary"
            android:textSize="12sp" />

    </LinearLayout>

    <!-- 删除按钮 -->
    <ImageView
        android:id="@+id/iv_delete"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_delete"
        android:tint="@color/error"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="4dp"
        android:clickable="true"
        android:focusable="true" />

</LinearLayout>