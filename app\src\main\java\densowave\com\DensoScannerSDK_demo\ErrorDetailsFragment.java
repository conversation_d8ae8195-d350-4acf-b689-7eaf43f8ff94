package densowave.com.DensoScannerSDK_demo;

import android.os.Bundle;
import android.text.method.ScrollingMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.warehousemanagement.R;

public class ErrorDetailsFragment extends Fragment {
    private TextView tvErrorDetails;
    private StringBuilder logBuffer = new StringBuilder(); // 用于在View创建前缓存日志

    public static ErrorDetailsFragment newInstance() {
        return new ErrorDetailsFragment();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_error_details, container, false);
        tvErrorDetails = view.findViewById(R.id.tv_error_details);
        
        // 设置滚动
        tvErrorDetails.setMovementMethod(new ScrollingMovementMethod());
        
        // 如果有缓存的日志，显示出来
        if (logBuffer.length() > 0) {
            tvErrorDetails.setText(logBuffer.toString());
            System.out.println("从缓存加载日志，长度: " + logBuffer.length());
        }
        
        return view;
    }

    public void appendErrorLog(String message) {
        try {
            String timestamp = new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date());
            String logMsg = timestamp + ": " + message + "\n";
            
            // 如果TextView还未创建，先缓存日志
            if (tvErrorDetails == null) {
                logBuffer.insert(0, logMsg);
                System.out.println("日志已缓存: " + message);
                return;
            }
            
            // 在UI线程中更新TextView
            if (getActivity() != null) {
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            String currentText = tvErrorDetails.getText().toString();
                            String newText = logMsg + currentText;
                            
                            // 限制日志长度，防止内存问题
                            if (newText.length() > 5000) {
                                newText = newText.substring(0, 5000);
                            }
                            
                            tvErrorDetails.setText(newText);
                            System.out.println("日志已添加到UI: " + message);
                        } catch (Exception e) {
                            e.printStackTrace();
                            System.out.println("设置日志文本异常: " + e.getMessage());
                        }
                    }
                });
            } else {
                System.out.println("Activity为null，无法添加日志: " + message);
                logBuffer.insert(0, logMsg);
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("添加日志异常: " + e.getMessage() + ", 原始消息: " + message);
        }
    }

    public String getErrorLog() {
        if (tvErrorDetails != null) {
            return tvErrorDetails.getText().toString();
        } else if (logBuffer.length() > 0) {
            return logBuffer.toString();
        }
        return "";
    }
}

