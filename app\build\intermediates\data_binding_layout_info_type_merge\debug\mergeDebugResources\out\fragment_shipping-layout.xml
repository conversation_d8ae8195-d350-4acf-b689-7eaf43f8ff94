<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_shipping" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\fragment_shipping.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_shipping_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="139" endOffset="14"/></Target><Target id="@+id/et_start_date" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="34" startOffset="20" endLine="39" endOffset="50"/></Target><Target id="@+id/et_end_date" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="51" startOffset="20" endLine="56" endOffset="50"/></Target><Target id="@+id/et_customer_filter" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="77" startOffset="20" endLine="80" endOffset="62"/></Target><Target id="@+id/et_remark_filter" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="92" startOffset="20" endLine="95" endOffset="62"/></Target><Target id="@+id/spinner_item_type" view="Spinner"><Expressions/><location startLine="102" startOffset="12" endLine="106" endOffset="52"/></Target><Target id="@+id/btn_search" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="109" startOffset="12" endLine="113" endOffset="46"/></Target><Target id="@+id/rv_shipping_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="120" startOffset="4" endLine="126" endOffset="44"/></Target><Target id="@+id/fab_add_shipping" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="129" startOffset="4" endLine="137" endOffset="45"/></Target></Targets></Layout>