<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_record" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\item_record.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_record_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="146" endOffset="51"/></Target><Target id="@+id/tv_record_id" view="TextView"><Expressions/><location startLine="24" startOffset="12" endLine="28" endOffset="43"/></Target><Target id="@+id/tv_status" view="TextView"><Expressions/><location startLine="31" startOffset="12" endLine="39" endOffset="41"/></Target><Target id="@+id/tv_date" view="TextView"><Expressions/><location startLine="55" startOffset="12" endLine="58" endOffset="43"/></Target><Target id="@+id/ll_customer" view="LinearLayout"><Expressions/><location startLine="63" startOffset="8" endLine="81" endOffset="22"/></Target><Target id="@+id/tv_customer" view="TextView"><Expressions/><location startLine="76" startOffset="12" endLine="79" endOffset="37"/></Target><Target id="@+id/ll_remark1" view="LinearLayout"><Expressions/><location startLine="84" startOffset="8" endLine="102" endOffset="22"/></Target><Target id="@+id/tv_remark1" view="TextView"><Expressions/><location startLine="97" startOffset="12" endLine="100" endOffset="37"/></Target><Target id="@+id/ll_remark2" view="LinearLayout"><Expressions/><location startLine="105" startOffset="8" endLine="123" endOffset="22"/></Target><Target id="@+id/tv_remark2" view="TextView"><Expressions/><location startLine="118" startOffset="12" endLine="121" endOffset="37"/></Target><Target id="@+id/tv_tag_count" view="TextView"><Expressions/><location startLine="136" startOffset="12" endLine="140" endOffset="52"/></Target></Targets></Layout>