{"logs": [{"outputFile": "com.example.warehousemanagement.app-mergeDebugResources-37:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c05fa92e389d5803b314d4541ae72f08\\transformed\\core-1.9.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "8093", "endColumns": "100", "endOffsets": "8189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e8cdd8f14c70e551ad9fcc8ba2fb4e74\\transformed\\material-1.9.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1070,1159,1238,1301,1394,1456,1522,1580,1653,1717,1773,1895,1952,2014,2070,2146,2280,2365,2451,2589,2670,2749,2839,2896,2947,3013,3091,3174,3262,3337,3416,3489,3560,3654,3732,3821,3911,3985,4066,4153,4206,4273,4354,4438,4500,4564,4627,4735,4836,4938,5041,5102,5157", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,85,81,76,97,93,96,121,80,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,85,137,80,78,89,56,50,65,77,82,87,74,78,72,70,93,77,88,89,73,80,86,52,66,80,83,61,63,62,107,100,101,102,60,54,80", "endOffsets": "264,350,432,509,607,701,798,920,1001,1065,1154,1233,1296,1389,1451,1517,1575,1648,1712,1768,1890,1947,2009,2065,2141,2275,2360,2446,2584,2665,2744,2834,2891,2942,3008,3086,3169,3257,3332,3411,3484,3555,3649,3727,3816,3906,3980,4061,4148,4201,4268,4349,4433,4495,4559,4622,4730,4831,4933,5036,5097,5152,5233"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3041,3127,3209,3286,3384,3478,3575,3697,3778,3842,3931,4010,4073,4166,4228,4294,4352,4425,4489,4545,4667,4724,4786,4842,4918,5052,5137,5223,5361,5442,5521,5611,5668,5719,5785,5863,5946,6034,6109,6188,6261,6332,6426,6504,6593,6683,6757,6838,6925,6978,7045,7126,7210,7272,7336,7399,7507,7608,7710,7813,7874,7929", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,85,81,76,97,93,96,121,80,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,85,137,80,78,89,56,50,65,77,82,87,74,78,72,70,93,77,88,89,73,80,86,52,66,80,83,61,63,62,107,100,101,102,60,54,80", "endOffsets": "314,3122,3204,3281,3379,3473,3570,3692,3773,3837,3926,4005,4068,4161,4223,4289,4347,4420,4484,4540,4662,4719,4781,4837,4913,5047,5132,5218,5356,5437,5516,5606,5663,5714,5780,5858,5941,6029,6104,6183,6256,6327,6421,6499,6588,6678,6752,6833,6920,6973,7040,7121,7205,7267,7331,7394,7502,7603,7705,7808,7869,7924,8005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ff72482a731973c8e0ce199a0d194ac5\\transformed\\appcompat-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,437,542,649,734,838,958,1036,1112,1204,1298,1393,1487,1587,1681,1777,1872,1964,2056,2138,2249,2352,2451,2566,2680,2783,2938,8010", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "432,537,644,729,833,953,1031,1107,1199,1293,1388,1482,1582,1676,1772,1867,1959,2051,2133,2244,2347,2446,2561,2675,2778,2933,3036,8088"}}]}]}