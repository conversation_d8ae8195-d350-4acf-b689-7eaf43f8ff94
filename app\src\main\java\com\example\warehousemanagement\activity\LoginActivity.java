package com.example.warehousemanagement.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.example.warehousemanagement.R;
import com.example.warehousemanagement.databinding.ActivityLoginBinding;
import com.example.warehousemanagement.network.ApiClient;
import com.example.warehousemanagement.network.ApiService;
import com.example.warehousemanagement.model.LoginRequest;
import com.example.warehousemanagement.model.LoginResponse;
import com.example.warehousemanagement.utils.PreferenceManager;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 登录Activity
 * 功能：用户登录验证
 */
public class LoginActivity extends AppCompatActivity {

    private ActivityLoginBinding binding;
    private ApiService apiService;
    private PreferenceManager preferenceManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityLoginBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 初始化
        initViews();
        initServices();

        // 检查是否已登录
        checkLoginStatus();
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        // 设置登录按钮点击事件
        binding.btnLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                performLogin();
            }
        });
    }

    /**
     * 初始化服务
     */
    private void initServices() {
        apiService = ApiClient.getInstance().getApiService();
        preferenceManager = PreferenceManager.getInstance(this);
    }

    /**
     * 检查登录状态
     */
    private void checkLoginStatus() {
        if (preferenceManager.isLoggedIn()) {
            // 已登录，直接跳转到主界面
            navigateToMain();
        }
    }

    /**
     * 执行登录
     */
    private void performLogin() {
        String username = binding.etUsername.getText().toString().trim();
        String password = binding.etPassword.getText().toString().trim();

        // 验证输入
        if (username.isEmpty()) {
            binding.etUsername.setError("请输入用户名");
            return;
        }

        if (password.isEmpty()) {
            binding.etPassword.setError("请输入密码");
            return;
        }

        // 显示加载状态
        showLoading(true);
        hideError();

        // 创建登录请求
        LoginRequest request = new LoginRequest(username, password);

        // 发送登录请求
        Call<LoginResponse> call = apiService.login(request);
        call.enqueue(new Callback<LoginResponse>() {
            @Override
            public void onResponse(Call<LoginResponse> call, Response<LoginResponse> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    LoginResponse loginResponse = response.body();
                    if (loginResponse.getCode() == 200) {
                        // 登录成功
                        handleLoginSuccess(loginResponse);
                    } else {
                        // 登录失败
                        showError(loginResponse.getMessage());
                    }
                } else {
                    showError("登录失败，请检查网络连接");
                }
            }

            @Override
            public void onFailure(Call<LoginResponse> call, Throwable t) {
                showLoading(false);
                showError("网络错误：" + t.getMessage());
            }
        });
    }

    /**
     * 处理登录成功
     */
    private void handleLoginSuccess(LoginResponse response) {
        try {
            // 检查响应数据
            if (response != null && response.getData() != null && response.getData().getUser() != null) {
                // 保存用户信息
                preferenceManager.saveUserInfo(response.getData().getUser());

                // 显示成功消息
                Toast.makeText(this, "登录成功", Toast.LENGTH_SHORT).show();

                // 跳转到主界面
                navigateToMain();
            } else {
                showError("登录响应数据异常");
            }
        } catch (Exception e) {
            showError("处理登录结果时发生错误：" + e.getMessage());
        }
    }

    /**
     * 跳转到主界面
     */
    private void navigateToMain() {
        Intent intent = new Intent(this, MainActivity.class);
        startActivity(intent);
        finish();
    }

    /**
     * 显示/隐藏加载状态
     */
    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        binding.btnLogin.setEnabled(!show);
    }

    /**
     * 显示错误信息
     */
    private void showError(String message) {
        binding.tvError.setText(message);
        binding.tvError.setVisibility(View.VISIBLE);
    }

    /**
     * 隐藏错误信息
     */
    private void hideError() {
        binding.tvError.setVisibility(View.GONE);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}
