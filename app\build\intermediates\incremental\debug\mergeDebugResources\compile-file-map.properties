#Fri May 30 14:37:59 GMT+08:00 2025
com.example.warehousemanagement.app-main-41\:/drawable/circle_background.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\drawable_circle_background.xml.flat
com.example.warehousemanagement.app-main-41\:/drawable/ic_arrow_back.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_arrow_back.xml.flat
com.example.warehousemanagement.app-main-41\:/drawable/ic_arrow_forward.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_arrow_forward.xml.flat
com.example.warehousemanagement.app-main-41\:/drawable/ic_delete.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_delete.xml.flat
com.example.warehousemanagement.app-main-41\:/drawable/ic_info.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_info.xml.flat
com.example.warehousemanagement.app-main-41\:/drawable/ic_inventory.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_inventory.xml.flat
com.example.warehousemanagement.app-main-41\:/drawable/ic_login.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_login.xml.flat
com.example.warehousemanagement.app-main-41\:/drawable/ic_person.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_person.xml.flat
com.example.warehousemanagement.app-main-41\:/drawable/ic_profile.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_profile.xml.flat
com.example.warehousemanagement.app-main-41\:/drawable/ic_recycling.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_recycling.xml.flat
com.example.warehousemanagement.app-main-41\:/drawable/ic_settings.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_settings.xml.flat
com.example.warehousemanagement.app-main-41\:/drawable/ic_shipping.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_shipping.xml.flat
com.example.warehousemanagement.app-main-41\:/drawable/ic_tag.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_tag.xml.flat
com.example.warehousemanagement.app-main-41\:/drawable/status_background.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\drawable_status_background.xml.flat
com.example.warehousemanagement.app-main-41\:/menu/bottom_navigation_menu.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\menu_bottom_navigation_menu.xml.flat
com.example.warehousemanagement.app-main-41\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher.png.flat
com.example.warehousemanagement.app-main-41\:/mipmap-hdpi/ic_launcher_round.png=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher_round.png.flat
com.example.warehousemanagement.app-main-41\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher.png.flat
com.example.warehousemanagement.app-main-41\:/mipmap-mdpi/ic_launcher_round.png=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher_round.png.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/activity_login.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_login.xml.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/activity_main.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main.xml.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/activity_recovery_add.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_recovery_add.xml.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/activity_settings.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_settings.xml.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/activity_shipping_add.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_shipping_add.xml.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/activity_simple_rfid.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_simple_rfid.xml.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/activity_storage_add.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_storage_add.xml.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/fragment_error_details.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_error_details.xml.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/fragment_profile.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_profile.xml.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/fragment_recovery.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_recovery.xml.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/fragment_scan_results.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_scan_results.xml.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/fragment_shipping.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_shipping.xml.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/fragment_storage.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_storage.xml.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/item_record.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_item_record.xml.flat
com.example.warehousemanagement.app-mergeDebugResources-38\:/layout/item_scanned_tag.xml=C\:\\Users\\mw808\\Desktop\\Box\\app\\build\\intermediates\\merged_res\\debug\\layout_item_scanned_tag.xml.flat
