-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:1-80:12
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:1-80:12
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:1-80:12
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:1-80:12
MERGED from [DENSOScannerSDK.aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a87d070892527ef2ad2180afc02c330\transformed\DENSOScannerSDK\AndroidManifest.xml:2:1-10:12
MERGED from [androidx.databinding:databinding-adapters:7.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e53a61425c54436f1504c86f5b3bf331\transformed\databinding-adapters-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:7.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e6dd0d2d14b38570b0675e56ff0f509\transformed\databinding-ktx-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:7.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecec38a330cbac868441310d3b5aa30c\transformed\databinding-runtime-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31b4f0bb6ee41689eef547c375f868b8\transformed\viewbinding-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8cdd8f14c70e551ad9fcc8ba2fb4e74\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67140b26e163fc2341e0cafac27817ef\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68da82d5067c8b58082a840cefff9791\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff72482a731973c8e0ce199a0d194ac5\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c57353fa174b699e2e469b00b81daae\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e257b58df5aaa0b6a17c3e0c8af554a9\transformed\fragment-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c85f23af8b8280c69160e7a43ca2b8ec\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c226b7556fe16c8f1017b1c066e7a9bb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92ac7cf4bad242c1ce0a7bb9825b65be\transformed\activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4484532d029a229444fc043fc0c980d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2afcd0d09317c2a2fb6486a201d05860\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d41e624c260f0be54392fd49d1e314\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5f7f131f850296f1787d5900e89a13f\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aad6fce41a22a27659d898fccb027360\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58a292081cf6bbd79cc4469ba11b3445\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae4764f2ede45ad8aba278e6816545c2\transformed\lifecycle-service-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\512443722c5b13d2bffe326918c64f45\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\106f446e66dfac508e457609693053eb\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60af168e6cd216b18bc2ffcc24b69849\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a428fcd2f91ce1e75c149473a725edf\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\332e9d6f3c76b646b967aaf86b639fe1\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36e49461ebab2cf1a6dc26d3bf8625f8\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b29c8d9d371a2a50c4728ab935c12eb6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01101660e5b438fcb5e34360809c4a6f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa703a8f114acc92c6f94126765217ee\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bcef66b7120823bd0c89af4547a569a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\505ae2c1eb5942e7a1a805484e6ea6dd\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\209d05a689ab70cc40a42a34590f2bf7\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fcd1dd1cd464edbfd885152038d730c6\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dfc63dc7743406573a0dca02ed50bec\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\048031d0b325d05cc0c6b44daf15ae9c\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2fe1ebeb6cfcc9526ce1c59b2d94d1cb\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e489d44a032b41782d360d8e10ceefbd\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36a290b914dd808238de3714ae0d1ba9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5ed20cda5921edaba1b83b38016a3d4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b077a8c37bcb4943e93a3f51f240112\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c326eb9560e6c7088bb51c1f229c5c3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05eb5982995dc87b664c84a7b9e1fc8f\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5cf6bb95b896f9ba38fa5c6a0b16f24\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b83467d7b1434a313a9017e31e92dd8\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa926b3a3863c24bf35b776fa9f9a595\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98f89881cfaf7e4a9e62fdc699ce48d9\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:1-80:12
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:1-80:12
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:1-80:12
	package
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:3:5-46
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:1-80:12
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:1-80:12
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.BLUETOOTH
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:6:5-68
MERGED from [DENSOScannerSDK.aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a87d070892527ef2ad2180afc02c330\transformed\DENSOScannerSDK\AndroidManifest.xml:7:5-68
MERGED from [DENSOScannerSDK.aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a87d070892527ef2ad2180afc02c330\transformed\DENSOScannerSDK\AndroidManifest.xml:7:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:6:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:7:5-74
MERGED from [DENSOScannerSDK.aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a87d070892527ef2ad2180afc02c330\transformed\DENSOScannerSDK\AndroidManifest.xml:8:5-74
MERGED from [DENSOScannerSDK.aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a87d070892527ef2ad2180afc02c330\transformed\DENSOScannerSDK\AndroidManifest.xml:8:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:7:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:9:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:9:22-73
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:10:5-73
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:10:22-70
uses-permission#android.permission.BLUETOOTH_ADVERTISE
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:11:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:11:22-75
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:13:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:13:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:14:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:14:22-78
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:15:5-85
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:15:22-82
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:17:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:17:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:18:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:18:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:19:5-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:19:22-79
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:21:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:21:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:22:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:23:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:23:22-73
uses-feature#android.hardware.bluetooth
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:25:5-87
	android:required
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:25:61-84
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:25:19-60
uses-feature#android.hardware.bluetooth_le
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:26:5-90
	android:required
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:26:64-87
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:26:19-63
application
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:28:5-78:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8cdd8f14c70e551ad9fcc8ba2fb4e74\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8cdd8f14c70e551ad9fcc8ba2fb4e74\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67140b26e163fc2341e0cafac27817ef\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67140b26e163fc2341e0cafac27817ef\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\106f446e66dfac508e457609693053eb\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\106f446e66dfac508e457609693053eb\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5ed20cda5921edaba1b83b38016a3d4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5ed20cda5921edaba1b83b38016a3d4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c326eb9560e6c7088bb51c1f229c5c3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c326eb9560e6c7088bb51c1f229c5c3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:34:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:32:9-41
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:33:9-54
	android:icon
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:31:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:30:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:35:9-57
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:29:9-45
activity#com.example.warehousemanagement.activity.LoginActivity
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:38:9-45:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:40:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:39:13-51
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:41:13-44:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:42:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:42:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:43:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:43:27-74
activity#com.example.warehousemanagement.activity.MainActivity
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:47:9-49:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:49:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:48:13-50
activity#com.example.warehousemanagement.activity.StorageAddActivity
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:52:9-55:67
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:55:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:54:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:53:13-56
activity#com.example.warehousemanagement.activity.ShippingAddActivity
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:57:9-60:67
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:60:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:59:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:58:13-57
activity#com.example.warehousemanagement.activity.RecoveryAddActivity
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:62:9-65:67
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:65:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:64:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:63:13-57
activity#com.example.warehousemanagement.activity.SettingsActivity
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:68:9-71:67
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:71:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:70:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:69:13-54
service#com.example.warehousemanagement.services.DataSyncService
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:74:9-77:40
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:76:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:77:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:75:13-53
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
MERGED from [DENSOScannerSDK.aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a87d070892527ef2ad2180afc02c330\transformed\DENSOScannerSDK\AndroidManifest.xml:5:5-44
MERGED from [DENSOScannerSDK.aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a87d070892527ef2ad2180afc02c330\transformed\DENSOScannerSDK\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:7.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e53a61425c54436f1504c86f5b3bf331\transformed\databinding-adapters-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:7.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e53a61425c54436f1504c86f5b3bf331\transformed\databinding-adapters-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:7.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e6dd0d2d14b38570b0675e56ff0f509\transformed\databinding-ktx-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:7.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e6dd0d2d14b38570b0675e56ff0f509\transformed\databinding-ktx-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:7.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecec38a330cbac868441310d3b5aa30c\transformed\databinding-runtime-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:7.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecec38a330cbac868441310d3b5aa30c\transformed\databinding-runtime-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31b4f0bb6ee41689eef547c375f868b8\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31b4f0bb6ee41689eef547c375f868b8\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8cdd8f14c70e551ad9fcc8ba2fb4e74\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8cdd8f14c70e551ad9fcc8ba2fb4e74\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67140b26e163fc2341e0cafac27817ef\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67140b26e163fc2341e0cafac27817ef\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68da82d5067c8b58082a840cefff9791\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68da82d5067c8b58082a840cefff9791\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff72482a731973c8e0ce199a0d194ac5\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff72482a731973c8e0ce199a0d194ac5\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c57353fa174b699e2e469b00b81daae\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c57353fa174b699e2e469b00b81daae\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e257b58df5aaa0b6a17c3e0c8af554a9\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e257b58df5aaa0b6a17c3e0c8af554a9\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c85f23af8b8280c69160e7a43ca2b8ec\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c85f23af8b8280c69160e7a43ca2b8ec\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c226b7556fe16c8f1017b1c066e7a9bb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c226b7556fe16c8f1017b1c066e7a9bb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92ac7cf4bad242c1ce0a7bb9825b65be\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92ac7cf4bad242c1ce0a7bb9825b65be\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4484532d029a229444fc043fc0c980d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4484532d029a229444fc043fc0c980d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2afcd0d09317c2a2fb6486a201d05860\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2afcd0d09317c2a2fb6486a201d05860\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d41e624c260f0be54392fd49d1e314\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d41e624c260f0be54392fd49d1e314\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5f7f131f850296f1787d5900e89a13f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5f7f131f850296f1787d5900e89a13f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aad6fce41a22a27659d898fccb027360\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aad6fce41a22a27659d898fccb027360\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58a292081cf6bbd79cc4469ba11b3445\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58a292081cf6bbd79cc4469ba11b3445\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae4764f2ede45ad8aba278e6816545c2\transformed\lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae4764f2ede45ad8aba278e6816545c2\transformed\lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\512443722c5b13d2bffe326918c64f45\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\512443722c5b13d2bffe326918c64f45\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\106f446e66dfac508e457609693053eb\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\106f446e66dfac508e457609693053eb\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60af168e6cd216b18bc2ffcc24b69849\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60af168e6cd216b18bc2ffcc24b69849\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a428fcd2f91ce1e75c149473a725edf\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a428fcd2f91ce1e75c149473a725edf\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\332e9d6f3c76b646b967aaf86b639fe1\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\332e9d6f3c76b646b967aaf86b639fe1\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36e49461ebab2cf1a6dc26d3bf8625f8\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36e49461ebab2cf1a6dc26d3bf8625f8\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b29c8d9d371a2a50c4728ab935c12eb6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b29c8d9d371a2a50c4728ab935c12eb6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01101660e5b438fcb5e34360809c4a6f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01101660e5b438fcb5e34360809c4a6f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa703a8f114acc92c6f94126765217ee\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa703a8f114acc92c6f94126765217ee\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bcef66b7120823bd0c89af4547a569a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bcef66b7120823bd0c89af4547a569a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\505ae2c1eb5942e7a1a805484e6ea6dd\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\505ae2c1eb5942e7a1a805484e6ea6dd\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\209d05a689ab70cc40a42a34590f2bf7\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\209d05a689ab70cc40a42a34590f2bf7\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fcd1dd1cd464edbfd885152038d730c6\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fcd1dd1cd464edbfd885152038d730c6\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dfc63dc7743406573a0dca02ed50bec\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dfc63dc7743406573a0dca02ed50bec\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\048031d0b325d05cc0c6b44daf15ae9c\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\048031d0b325d05cc0c6b44daf15ae9c\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2fe1ebeb6cfcc9526ce1c59b2d94d1cb\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2fe1ebeb6cfcc9526ce1c59b2d94d1cb\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e489d44a032b41782d360d8e10ceefbd\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e489d44a032b41782d360d8e10ceefbd\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36a290b914dd808238de3714ae0d1ba9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36a290b914dd808238de3714ae0d1ba9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5ed20cda5921edaba1b83b38016a3d4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5ed20cda5921edaba1b83b38016a3d4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b077a8c37bcb4943e93a3f51f240112\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b077a8c37bcb4943e93a3f51f240112\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c326eb9560e6c7088bb51c1f229c5c3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c326eb9560e6c7088bb51c1f229c5c3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05eb5982995dc87b664c84a7b9e1fc8f\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05eb5982995dc87b664c84a7b9e1fc8f\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5cf6bb95b896f9ba38fa5c6a0b16f24\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5cf6bb95b896f9ba38fa5c6a0b16f24\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b83467d7b1434a313a9017e31e92dd8\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b83467d7b1434a313a9017e31e92dd8\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa926b3a3863c24bf35b776fa9f9a595\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa926b3a3863c24bf35b776fa9f9a595\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98f89881cfaf7e4a9e62fdc699ce48d9\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98f89881cfaf7e4a9e62fdc699ce48d9\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\106f446e66dfac508e457609693053eb\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\106f446e66dfac508e457609693053eb\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c326eb9560e6c7088bb51c1f229c5c3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c326eb9560e6c7088bb51c1f229c5c3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\106f446e66dfac508e457609693053eb\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\106f446e66dfac508e457609693053eb\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\106f446e66dfac508e457609693053eb\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.example.warehousemanagement.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.warehousemanagement.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05fa92e389d5803b314d4541ae72f08\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
