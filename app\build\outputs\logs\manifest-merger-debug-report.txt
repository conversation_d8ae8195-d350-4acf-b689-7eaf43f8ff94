-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:1-80:12
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:1-80:12
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:1-80:12
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:1-80:12
MERGED from [DENSOScannerSDK.aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f65664ccf018c808a64b3ab1fd6199\transformed\DENSOScannerSDK\AndroidManifest.xml:2:1-10:12
MERGED from [androidx.databinding:databinding-adapters:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90e344e85c584f113ea612d265ccbd7c\transformed\databinding-adapters-8.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7af9c0b4762cee355c647a1e9b54e8e\transformed\databinding-ktx-8.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f405dc4c4a2e7cb5f834d6576522c6b1\transformed\databinding-runtime-8.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a968285984074824984ccb8888f8f1\transformed\viewbinding-8.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751a317a1d40c660b2a58fb1ab3f0187\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b472344c1d02279c842950179cdee44\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83486373376e1c239e8f4fb78503c430\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24ccdfb3f311aaa88a834c682a4cfd8c\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c902c3c9a42f60cb68e868a4de7e97ec\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fa87bea8d3064991c4950a7cd31f392\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fa2cbc1f3e1c8856afb02344e16735a\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b3d6ddbff50fb2b51632937a346b845\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bb473449638126c59b02dd81dfc5e82\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1b135366676b31e1296cde23c82c3b4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b24cfd7c61b4692598affa6a7d68e10\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d14e6ace58077bebd3821a913a07c5f4\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25056fb77a48753cfdf81779f70296c1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca78bbe36db3fa0312c0dcd46cfeeab\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5426231ab31392e5b33d42e73e9b9039\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b521a8f99434b292da05a81a19c7f8a\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e1a7cf70cd8cd87f634208a3f6bd915\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e9035f9d6f4e33d230b07a0b638c7a4\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60497f4189655a1239df256971621998\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b122ad9efa6235fdb3f87bd08421739d\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dac543713abd27cc006a85d011382eca\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23f05609e6ea6ed25bdeb3eac7e836dd\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ecfc0cc380d210e7f8940c367861d5b\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7724cca69854e32a179bdbbb5684022c\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2338b0d2fd806bb6b95f92bddd790be\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3c20c358e94118bee9b4161e561eb71\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b892bf1b5c357be5723ee23b33fcb41\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b1c7ec8210b084c434d494ebe34ba35\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276436669951c331185db303038fee50\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09955d63a5c12524cf9897c542294efa\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d484dbbbc102e0e00b4a720c4b7b9c\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78b878a2477ffed38acd39b519b92118\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa6b54dbb4dabb4ce6cbe6398c571b63\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d65dda3679d2a5857597b84739043fdd\transformed\activity-compose-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17e9d6fbd02b1f6532001d32447401d8\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f59d6184fbb7b13a43e9470c4b9121cd\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5540969351275aa04354ff915440de3\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d278763173dc32d6738b3015f790ea2\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0cc0c09111ecd9290e982293188b285\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aa36e8a18b861399a83c0803aa4fc37\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7656c915895f1ce994b8e47353e6f7b5\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fd6d133b4f68099e9a0be51087f77ee\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9c9192828977ce370dde14435a3dd64\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c54f0fb8e46aebdab17481f77571f1d5\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58033aa3fe5ad325da833bd941cc5580\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2680da2e6456ba610d9a057d6d7dbd1c\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a4d97e35115b97b7166c47a6512594c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67c50efec1c25d65ae08b1ca021dde42\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c5e0076d32ae7c88f31a85bb12e3215\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8247a4d041b3da5690fdb6b6e86f06b0\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a544610fc6f3b681623a4482e3dff666\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fb91530ccf4ec3582924f10c0e4a953\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eb39b49ba5be68e6649c6773d11067\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f795e9a007962968e1e605d54e80d471\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d62c1c1c0ea1bf87fa3e0c0d7b65592\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3a257e4564684bb9a1a45d3213fa2d1\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91deca6799a1cf6745630877ddaaea3d\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbc11017a7b8e97b301617e73ce9a8ca\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06ace879758102d28a4f1ab5aa473a85\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcf6071f8ae71f7f344ccc70fef4a2d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1eec52a1e549dbbf8703d25e29e508b\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27966868919fe5a399ef07c57960784d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9577bcd0e8f7d8a3b1dd74d6f41579ab\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f01e8e32c47f3ce3fba4dfa932d35e8\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fda305ae30c042fb7c0a9108e567ebf5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9ecf7b060063059954dbd975563108b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f62fe76726d959afc2189ecef896076c\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08f1b241c3d0aadd668006bf5cf86eb7\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa1a5e2f2aa77b0c77594ecf29aa21a9\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7718c7d11ce4a2017d06451a5f0d0e1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d151c324f6c6c5c487028561c192335e\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:3:5-46
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.BLUETOOTH
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:6:5-68
MERGED from [DENSOScannerSDK.aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f65664ccf018c808a64b3ab1fd6199\transformed\DENSOScannerSDK\AndroidManifest.xml:7:5-68
MERGED from [DENSOScannerSDK.aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f65664ccf018c808a64b3ab1fd6199\transformed\DENSOScannerSDK\AndroidManifest.xml:7:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:6:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:7:5-74
MERGED from [DENSOScannerSDK.aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f65664ccf018c808a64b3ab1fd6199\transformed\DENSOScannerSDK\AndroidManifest.xml:8:5-74
MERGED from [DENSOScannerSDK.aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f65664ccf018c808a64b3ab1fd6199\transformed\DENSOScannerSDK\AndroidManifest.xml:8:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:7:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:9:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:9:22-73
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:10:5-73
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:10:22-70
uses-permission#android.permission.BLUETOOTH_ADVERTISE
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:11:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:11:22-75
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:13:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:13:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:14:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:14:22-78
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:15:5-85
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:15:22-82
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:17:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:17:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:18:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:18:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:19:5-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:19:22-79
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:21:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:21:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:22:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:23:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:23:22-73
uses-feature#android.hardware.bluetooth
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:25:5-87
	android:required
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:25:61-84
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:25:19-60
uses-feature#android.hardware.bluetooth_le
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:26:5-90
	android:required
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:26:64-87
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:26:19-63
application
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:28:5-78:19
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:28:5-78:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751a317a1d40c660b2a58fb1ab3f0187\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751a317a1d40c660b2a58fb1ab3f0187\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b472344c1d02279c842950179cdee44\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b472344c1d02279c842950179cdee44\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276436669951c331185db303038fee50\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276436669951c331185db303038fee50\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d484dbbbc102e0e00b4a720c4b7b9c\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d484dbbbc102e0e00b4a720c4b7b9c\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fd6d133b4f68099e9a0be51087f77ee\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fd6d133b4f68099e9a0be51087f77ee\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fb91530ccf4ec3582924f10c0e4a953\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fb91530ccf4ec3582924f10c0e4a953\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27966868919fe5a399ef07c57960784d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27966868919fe5a399ef07c57960784d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9ecf7b060063059954dbd975563108b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9ecf7b060063059954dbd975563108b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:34:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:32:9-41
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:33:9-54
	android:icon
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:31:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:30:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:35:9-57
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:29:9-45
activity#com.example.warehousemanagement.activity.LoginActivity
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:38:9-45:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:40:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:39:13-51
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:41:13-44:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:42:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:42:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:43:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:43:27-74
activity#com.example.warehousemanagement.activity.MainActivity
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:47:9-49:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:49:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:48:13-50
activity#com.example.warehousemanagement.activity.StorageAddActivity
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:52:9-55:67
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:55:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:54:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:53:13-56
activity#com.example.warehousemanagement.activity.ShippingAddActivity
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:57:9-60:67
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:60:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:59:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:58:13-57
activity#com.example.warehousemanagement.activity.RecoveryAddActivity
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:62:9-65:67
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:65:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:64:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:63:13-57
activity#com.example.warehousemanagement.activity.SettingsActivity
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:68:9-71:67
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:71:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:70:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:69:13-54
service#com.example.warehousemanagement.services.DataSyncService
ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:74:9-77:40
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:76:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:77:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:75:13-53
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
MERGED from [DENSOScannerSDK.aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f65664ccf018c808a64b3ab1fd6199\transformed\DENSOScannerSDK\AndroidManifest.xml:5:5-44
MERGED from [DENSOScannerSDK.aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f65664ccf018c808a64b3ab1fd6199\transformed\DENSOScannerSDK\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90e344e85c584f113ea612d265ccbd7c\transformed\databinding-adapters-8.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90e344e85c584f113ea612d265ccbd7c\transformed\databinding-adapters-8.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7af9c0b4762cee355c647a1e9b54e8e\transformed\databinding-ktx-8.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7af9c0b4762cee355c647a1e9b54e8e\transformed\databinding-ktx-8.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f405dc4c4a2e7cb5f834d6576522c6b1\transformed\databinding-runtime-8.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f405dc4c4a2e7cb5f834d6576522c6b1\transformed\databinding-runtime-8.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a968285984074824984ccb8888f8f1\transformed\viewbinding-8.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a968285984074824984ccb8888f8f1\transformed\viewbinding-8.8.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751a317a1d40c660b2a58fb1ab3f0187\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751a317a1d40c660b2a58fb1ab3f0187\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b472344c1d02279c842950179cdee44\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b472344c1d02279c842950179cdee44\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83486373376e1c239e8f4fb78503c430\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83486373376e1c239e8f4fb78503c430\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24ccdfb3f311aaa88a834c682a4cfd8c\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24ccdfb3f311aaa88a834c682a4cfd8c\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c902c3c9a42f60cb68e868a4de7e97ec\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c902c3c9a42f60cb68e868a4de7e97ec\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fa87bea8d3064991c4950a7cd31f392\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fa87bea8d3064991c4950a7cd31f392\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fa2cbc1f3e1c8856afb02344e16735a\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fa2cbc1f3e1c8856afb02344e16735a\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b3d6ddbff50fb2b51632937a346b845\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b3d6ddbff50fb2b51632937a346b845\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bb473449638126c59b02dd81dfc5e82\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bb473449638126c59b02dd81dfc5e82\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1b135366676b31e1296cde23c82c3b4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1b135366676b31e1296cde23c82c3b4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b24cfd7c61b4692598affa6a7d68e10\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b24cfd7c61b4692598affa6a7d68e10\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d14e6ace58077bebd3821a913a07c5f4\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d14e6ace58077bebd3821a913a07c5f4\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25056fb77a48753cfdf81779f70296c1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25056fb77a48753cfdf81779f70296c1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca78bbe36db3fa0312c0dcd46cfeeab\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca78bbe36db3fa0312c0dcd46cfeeab\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5426231ab31392e5b33d42e73e9b9039\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5426231ab31392e5b33d42e73e9b9039\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b521a8f99434b292da05a81a19c7f8a\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b521a8f99434b292da05a81a19c7f8a\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e1a7cf70cd8cd87f634208a3f6bd915\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e1a7cf70cd8cd87f634208a3f6bd915\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e9035f9d6f4e33d230b07a0b638c7a4\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e9035f9d6f4e33d230b07a0b638c7a4\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60497f4189655a1239df256971621998\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60497f4189655a1239df256971621998\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b122ad9efa6235fdb3f87bd08421739d\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b122ad9efa6235fdb3f87bd08421739d\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dac543713abd27cc006a85d011382eca\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dac543713abd27cc006a85d011382eca\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23f05609e6ea6ed25bdeb3eac7e836dd\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23f05609e6ea6ed25bdeb3eac7e836dd\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ecfc0cc380d210e7f8940c367861d5b\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ecfc0cc380d210e7f8940c367861d5b\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7724cca69854e32a179bdbbb5684022c\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7724cca69854e32a179bdbbb5684022c\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2338b0d2fd806bb6b95f92bddd790be\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2338b0d2fd806bb6b95f92bddd790be\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3c20c358e94118bee9b4161e561eb71\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3c20c358e94118bee9b4161e561eb71\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b892bf1b5c357be5723ee23b33fcb41\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b892bf1b5c357be5723ee23b33fcb41\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b1c7ec8210b084c434d494ebe34ba35\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b1c7ec8210b084c434d494ebe34ba35\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276436669951c331185db303038fee50\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276436669951c331185db303038fee50\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09955d63a5c12524cf9897c542294efa\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09955d63a5c12524cf9897c542294efa\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d484dbbbc102e0e00b4a720c4b7b9c\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d484dbbbc102e0e00b4a720c4b7b9c\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78b878a2477ffed38acd39b519b92118\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78b878a2477ffed38acd39b519b92118\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa6b54dbb4dabb4ce6cbe6398c571b63\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa6b54dbb4dabb4ce6cbe6398c571b63\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d65dda3679d2a5857597b84739043fdd\transformed\activity-compose-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d65dda3679d2a5857597b84739043fdd\transformed\activity-compose-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17e9d6fbd02b1f6532001d32447401d8\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17e9d6fbd02b1f6532001d32447401d8\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f59d6184fbb7b13a43e9470c4b9121cd\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f59d6184fbb7b13a43e9470c4b9121cd\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5540969351275aa04354ff915440de3\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5540969351275aa04354ff915440de3\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d278763173dc32d6738b3015f790ea2\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d278763173dc32d6738b3015f790ea2\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0cc0c09111ecd9290e982293188b285\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0cc0c09111ecd9290e982293188b285\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aa36e8a18b861399a83c0803aa4fc37\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aa36e8a18b861399a83c0803aa4fc37\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7656c915895f1ce994b8e47353e6f7b5\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7656c915895f1ce994b8e47353e6f7b5\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fd6d133b4f68099e9a0be51087f77ee\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fd6d133b4f68099e9a0be51087f77ee\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9c9192828977ce370dde14435a3dd64\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9c9192828977ce370dde14435a3dd64\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c54f0fb8e46aebdab17481f77571f1d5\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c54f0fb8e46aebdab17481f77571f1d5\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58033aa3fe5ad325da833bd941cc5580\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58033aa3fe5ad325da833bd941cc5580\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2680da2e6456ba610d9a057d6d7dbd1c\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2680da2e6456ba610d9a057d6d7dbd1c\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a4d97e35115b97b7166c47a6512594c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a4d97e35115b97b7166c47a6512594c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67c50efec1c25d65ae08b1ca021dde42\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67c50efec1c25d65ae08b1ca021dde42\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c5e0076d32ae7c88f31a85bb12e3215\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c5e0076d32ae7c88f31a85bb12e3215\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8247a4d041b3da5690fdb6b6e86f06b0\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8247a4d041b3da5690fdb6b6e86f06b0\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a544610fc6f3b681623a4482e3dff666\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a544610fc6f3b681623a4482e3dff666\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fb91530ccf4ec3582924f10c0e4a953\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fb91530ccf4ec3582924f10c0e4a953\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eb39b49ba5be68e6649c6773d11067\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eb39b49ba5be68e6649c6773d11067\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f795e9a007962968e1e605d54e80d471\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f795e9a007962968e1e605d54e80d471\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d62c1c1c0ea1bf87fa3e0c0d7b65592\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d62c1c1c0ea1bf87fa3e0c0d7b65592\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3a257e4564684bb9a1a45d3213fa2d1\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3a257e4564684bb9a1a45d3213fa2d1\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91deca6799a1cf6745630877ddaaea3d\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91deca6799a1cf6745630877ddaaea3d\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbc11017a7b8e97b301617e73ce9a8ca\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbc11017a7b8e97b301617e73ce9a8ca\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06ace879758102d28a4f1ab5aa473a85\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06ace879758102d28a4f1ab5aa473a85\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcf6071f8ae71f7f344ccc70fef4a2d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcf6071f8ae71f7f344ccc70fef4a2d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1eec52a1e549dbbf8703d25e29e508b\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1eec52a1e549dbbf8703d25e29e508b\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27966868919fe5a399ef07c57960784d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27966868919fe5a399ef07c57960784d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9577bcd0e8f7d8a3b1dd74d6f41579ab\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9577bcd0e8f7d8a3b1dd74d6f41579ab\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f01e8e32c47f3ce3fba4dfa932d35e8\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f01e8e32c47f3ce3fba4dfa932d35e8\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fda305ae30c042fb7c0a9108e567ebf5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fda305ae30c042fb7c0a9108e567ebf5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9ecf7b060063059954dbd975563108b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9ecf7b060063059954dbd975563108b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f62fe76726d959afc2189ecef896076c\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f62fe76726d959afc2189ecef896076c\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08f1b241c3d0aadd668006bf5cf86eb7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08f1b241c3d0aadd668006bf5cf86eb7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa1a5e2f2aa77b0c77594ecf29aa21a9\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa1a5e2f2aa77b0c77594ecf29aa21a9\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7718c7d11ce4a2017d06451a5f0d0e1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7718c7d11ce4a2017d06451a5f0d0e1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d151c324f6c6c5c487028561c192335e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d151c324f6c6c5c487028561c192335e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276436669951c331185db303038fee50\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276436669951c331185db303038fee50\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276436669951c331185db303038fee50\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d484dbbbc102e0e00b4a720c4b7b9c\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d484dbbbc102e0e00b4a720c4b7b9c\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d484dbbbc102e0e00b4a720c4b7b9c\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fd6d133b4f68099e9a0be51087f77ee\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fb91530ccf4ec3582924f10c0e4a953\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fb91530ccf4ec3582924f10c0e4a953\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9ecf7b060063059954dbd975563108b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9ecf7b060063059954dbd975563108b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fd6d133b4f68099e9a0be51087f77ee\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fd6d133b4f68099e9a0be51087f77ee\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fd6d133b4f68099e9a0be51087f77ee\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fd6d133b4f68099e9a0be51087f77ee\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fd6d133b4f68099e9a0be51087f77ee\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fd6d133b4f68099e9a0be51087f77ee\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fd6d133b4f68099e9a0be51087f77ee\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.warehousemanagement.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.warehousemanagement.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fb91530ccf4ec3582924f10c0e4a953\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fb91530ccf4ec3582924f10c0e4a953\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fb91530ccf4ec3582924f10c0e4a953\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
