<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_simple_rfid" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\activity_simple_rfid.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_simple_rfid_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="82" endOffset="14"/></Target><Target id="@+id/tv_connection_status" view="TextView"><Expressions/><location startLine="23" startOffset="8" endLine="30" endOffset="46"/></Target><Target id="@+id/btn_connect" view="Button"><Expressions/><location startLine="32" startOffset="8" endLine="37" endOffset="37"/></Target><Target id="@+id/et_write_content" view="EditText"><Expressions/><location startLine="49" startOffset="8" endLine="55" endOffset="37"/></Target><Target id="@+id/btn_write_tag" view="Button"><Expressions/><location startLine="57" startOffset="8" endLine="63" endOffset="46"/></Target><Target id="@+id/tab_layout" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="68" startOffset="4" endLine="73" endOffset="31"/></Target><Target id="@+id/view_pager" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="76" startOffset="4" endLine="80" endOffset="35"/></Target></Targets></Layout>