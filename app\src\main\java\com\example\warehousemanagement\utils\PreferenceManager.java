package com.example.warehousemanagement.utils;

import android.content.Context;
import android.content.SharedPreferences;
import com.example.warehousemanagement.model.User;
import com.google.gson.Gson;

/**
 * 偏好设置管理器
 * 管理用户登录状态和设置信息
 */
public class PreferenceManager {
    
    private static final String PREF_NAME = "warehouse_management";
    private static final String KEY_IS_LOGGED_IN = "is_logged_in";
    private static final String KEY_USER_INFO = "user_info";
    
    private static PreferenceManager instance;
    private SharedPreferences preferences;
    private Gson gson;
    
    private PreferenceManager(Context context) {
        preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        gson = new Gson();
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized PreferenceManager getInstance(Context context) {
        if (instance == null) {
            instance = new PreferenceManager(context.getApplicationContext());
        }
        return instance;
    }
    
    /**
     * 保存用户信息
     */
    public void saveUserInfo(User user) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(KEY_IS_LOGGED_IN, true);
        editor.putString(KEY_USER_INFO, gson.toJson(user));
        editor.apply();
    }
    
    /**
     * 获取用户信息
     */
    public User getUserInfo() {
        String userJson = preferences.getString(KEY_USER_INFO, null);
        if (userJson != null) {
            return gson.fromJson(userJson, User.class);
        }
        return null;
    }
    
    /**
     * 检查是否已登录
     */
    public boolean isLoggedIn() {
        return preferences.getBoolean(KEY_IS_LOGGED_IN, false);
    }
    
    /**
     * 清除用户信息（退出登录）
     */
    public void clearUserInfo() {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(KEY_IS_LOGGED_IN, false);
        editor.remove(KEY_USER_INFO);
        editor.apply();
    }
    
    /**
     * 保存设置
     */
    public void saveSetting(String key, String value) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(key, value);
        editor.apply();
    }
    
    /**
     * 保存设置
     */
    public void saveSetting(String key, int value) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putInt(key, value);
        editor.apply();
    }
    
    /**
     * 保存设置
     */
    public void saveSetting(String key, boolean value) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(key, value);
        editor.apply();
    }
    
    /**
     * 获取字符串设置
     */
    public String getStringSetting(String key, String defaultValue) {
        return preferences.getString(key, defaultValue);
    }
    
    /**
     * 获取整数设置
     */
    public int getIntSetting(String key, int defaultValue) {
        return preferences.getInt(key, defaultValue);
    }
    
    /**
     * 获取布尔设置
     */
    public boolean getBooleanSetting(String key, boolean defaultValue) {
        return preferences.getBoolean(key, defaultValue);
    }
}
