package com.example.warehousemanagement.model;

/**
 * 系统设置模型
 */
public class Settings {
    // 服务器设置
    private String serverUrl;
    private int timeout;

    // RFID设置
    private boolean autoConnect;
    private int scanPower;

    // 数据设置
    private boolean autoSync;
    private int syncInterval;

    // 原有设置（保持兼容性）
    private boolean notificationEnabled;
    private int boxExpiryDays;
    private int bagExpiryDays;
    private int recoveryDays;
    private int scanDistance;

    // 服务器设置的getter和setter
    public String getServerUrl() {
        return serverUrl;
    }

    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }

    // RFID设置的getter和setter
    public boolean isAutoConnect() {
        return autoConnect;
    }

    public void setAutoConnect(boolean autoConnect) {
        this.autoConnect = autoConnect;
    }

    public int getScanPower() {
        return scanPower;
    }

    public void setScanPower(int scanPower) {
        this.scanPower = scanPower;
    }

    // 数据设置的getter和setter
    public boolean isAutoSync() {
        return autoSync;
    }

    public void setAutoSync(boolean autoSync) {
        this.autoSync = autoSync;
    }

    public int getSyncInterval() {
        return syncInterval;
    }

    public void setSyncInterval(int syncInterval) {
        this.syncInterval = syncInterval;
    }

    // 原有设置的getter和setter（保持兼容性）
    public boolean isNotificationEnabled() {
        return notificationEnabled;
    }

    public void setNotificationEnabled(boolean notificationEnabled) {
        this.notificationEnabled = notificationEnabled;
    }

    public int getBoxExpiryDays() {
        return boxExpiryDays;
    }

    public void setBoxExpiryDays(int boxExpiryDays) {
        this.boxExpiryDays = boxExpiryDays;
    }

    public int getBagExpiryDays() {
        return bagExpiryDays;
    }

    public void setBagExpiryDays(int bagExpiryDays) {
        this.bagExpiryDays = bagExpiryDays;
    }

    public int getRecoveryDays() {
        return recoveryDays;
    }

    public void setRecoveryDays(int recoveryDays) {
        this.recoveryDays = recoveryDays;
    }

    public int getScanDistance() {
        return scanDistance;
    }

    public void setScanDistance(int scanDistance) {
        this.scanDistance = scanDistance;
    }
}
