[{"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/fragment_recovery.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/fragment_recovery.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/fragment_storage.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/fragment_storage.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/activity_shipping_add.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/activity_shipping_add.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/activity_login.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/activity_login.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/item_scanned_tag.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/item_scanned_tag.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/activity_storage_add.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/activity_storage_add.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/fragment_profile.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/fragment_profile.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/activity_main.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/activity_main.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/activity_simple_rfid.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/activity_simple_rfid.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/fragment_shipping.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/fragment_shipping.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/item_record.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/item_record.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/activity_recovery_add.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/activity_recovery_add.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/fragment_error_details.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/fragment_error_details.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/fragment_scan_results.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/fragment_scan_results.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-36:/layout/activity_settings.xml", "source": "com.example.warehousemanagement.app-main-39:/layout/activity_settings.xml"}]