<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background"
    android:padding="24dp"
    android:gravity="center">

    <!-- Logo区域 -->
    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="32dp"
        android:src="@drawable/ic_login"
        android:contentDescription="@string/app_name" />

    <!-- 标题 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/login_title"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginBottom="32dp" />

    <!-- 登录表单卡片 -->
    <com.google.android.material.card.MaterialCardView
        style="@style/CardStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 用户名输入框 -->
            <com.google.android.material.textfield.TextInputLayout
                style="@style/EditTextStyle"
                android:hint="@string/username_hint">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_username"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 密码输入框 -->
            <com.google.android.material.textfield.TextInputLayout
                style="@style/EditTextStyle"
                android:hint="@string/password_hint"
                app:passwordToggleEnabled="true">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 登录按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_login"
                style="@style/PrimaryButton"
                android:text="@string/login_button"
                android:layout_marginTop="16dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 错误提示 -->
    <TextView
        android:id="@+id/tv_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/error"
        android:textSize="14sp"
        android:visibility="gone"
        android:layout_marginTop="16dp" />

    <!-- 加载进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:visibility="gone" />

</LinearLayout>