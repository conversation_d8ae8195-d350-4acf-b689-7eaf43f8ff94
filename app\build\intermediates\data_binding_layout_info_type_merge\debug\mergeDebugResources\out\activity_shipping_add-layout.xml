<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_shipping_add" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\activity_shipping_add.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_shipping_add_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="219" endOffset="14"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="9" startOffset="4" endLine="17" endOffset="54"/></Target><Target id="@+id/tv_rfid_status" view="TextView"><Expressions/><location startLine="47" startOffset="20" endLine="52" endOffset="60"/></Target><Target id="@+id/btn_scan" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="55" startOffset="20" endLine="59" endOffset="54"/></Target><Target id="@+id/tv_tag_count" view="TextView"><Expressions/><location startLine="88" startOffset="24" endLine="91" endOffset="48"/></Target><Target id="@+id/rv_scanned_tags" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="96" startOffset="20" endLine="100" endOffset="64"/></Target><Target id="@+id/tv_empty_tags" view="TextView"><Expressions/><location startLine="103" startOffset="20" endLine="111" endOffset="54"/></Target><Target id="@+id/et_customer_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="138" startOffset="24" endLine="141" endOffset="66"/></Target><Target id="@+id/et_shipping_date" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="150" startOffset="24" endLine="155" endOffset="54"/></Target><Target id="@+id/et_remark1" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="164" startOffset="24" endLine="167" endOffset="66"/></Target><Target id="@+id/et_remark2" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="176" startOffset="24" endLine="180" endOffset="63"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="201" startOffset="8" endLine="207" endOffset="43"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="209" startOffset="8" endLine="215" endOffset="41"/></Target></Targets></Layout>