package com.example.warehousemanagement.rfid;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import com.densowave.scannersdk.Common.CommException;
import com.densowave.scannersdk.Common.CommManager;
import com.densowave.scannersdk.Common.CommScanner;
import com.densowave.scannersdk.Common.CommStatusChangedEvent;
import com.densowave.scannersdk.Const.CommConst;
import com.densowave.scannersdk.Listener.RFIDDataDelegate;
import com.densowave.scannersdk.Listener.ScannerAcceptStatusListener;
import com.densowave.scannersdk.Listener.ScannerStatusListener;
import com.densowave.scannersdk.RFID.RFIDDataReceivedEvent;
import java.util.ArrayList;
import java.util.List;

/**
 * RFID管理器单例类
 * 功能：管理RFID设备连接、扫描、数据接收
 * 参考SimpleRfidActivity.java实现
 */
public class RfidManager implements RFIDDataDelegate, ScannerStatusListener, ScannerAcceptStatusListener {

    private static final String TAG = "RfidManager";
    private static RfidManager instance;

    private Context context;
    private CommScanner commScanner;
    private boolean scannerConnected = false;
    private boolean isConnecting = false;
    private boolean isScanning = false;

    private Handler mainHandler;
    private List<RfidDataListener> dataListeners;
    private List<RfidStatusListener> statusListeners;

    // 连接重试相关
    private int retryCount = 0;
    private static final int MAX_RETRY_COUNT = 3;
    private static final int RETRY_DELAY_MS = 2000;

    /**
     * RFID数据监听器接口
     */
    public interface RfidDataListener {
        void onRfidDataReceived(List<String> tagIds);
    }

    /**
     * RFID状态监听器接口
     */
    public interface RfidStatusListener {
        void onConnectionStatusChanged(boolean connected);
        void onScanningStatusChanged(boolean scanning);
        void onError(String error);
    }

    /**
     * 获取单例实例
     */
    public static synchronized RfidManager getInstance() {
        if (instance == null) {
            instance = new RfidManager();
        }
        return instance;
    }

    private RfidManager() {
        mainHandler = new Handler(Looper.getMainLooper());
        dataListeners = new ArrayList<>();
        statusListeners = new ArrayList<>();
    }

    /**
     * 初始化RFID管理器
     */
    public void initialize(Context context) {
        this.context = context.getApplicationContext();
        Log.d(TAG, "RFID管理器初始化完成");
    }

    /**
     * 自动连接RFID设备
     */
    public void autoConnect() {
        if (!scannerConnected && !isConnecting) {
            connectToScanner();
        }
    }

    /**
     * 连接到RFID设备
     */
    public void connectToScanner() {
        if (isConnecting) {
            Log.d(TAG, "正在连接中，忽略重复连接请求");
            return;
        }

        try {
            isConnecting = true;
            retryCount = 0;
            Log.d(TAG, "开始连接RFID设备");

            notifyStatusListeners(status -> status.onConnectionStatusChanged(false));

            CommManager.addAcceptStatusListener(this);
            CommManager.startAccept();

        } catch (Exception e) {
            Log.e(TAG, "连接RFID设备异常", e);
            isConnecting = false;
            notifyStatusListeners(status -> status.onError("连接设备失败: " + e.getMessage()));
        }
    }

    /**
     * 断开RFID设备连接
     */
    public void disconnect() {
        try {
            if (commScanner != null) {
                stopScanning();
                commScanner.close();
                commScanner.removeStatusListener(this);
                commScanner = null;
            }

            scannerConnected = false;
            isConnecting = false;

            CommManager.endAccept();
            CommManager.removeAcceptStatusListener(this);

            notifyStatusListeners(status -> status.onConnectionStatusChanged(false));
            Log.d(TAG, "RFID设备已断开连接");

        } catch (CommException e) {
            Log.e(TAG, "断开连接异常", e);
            notifyStatusListeners(status -> status.onError("断开连接失败: " + e.getMessage()));
        }
    }

    /**
     * 开始扫描
     */
    public void startScanning() {
        if (!scannerConnected) {
            Log.w(TAG, "设备未连接，无法开始扫描");
            notifyStatusListeners(status -> status.onError("设备未连接"));
            return;
        }

        if (isScanning) {
            Log.d(TAG, "已在扫描中");
            return;
        }

        try {
            commScanner.getRFIDScanner().setDataDelegate(this);
            commScanner.getRFIDScanner().openInventory();
            isScanning = true;

            notifyStatusListeners(status -> status.onScanningStatusChanged(true));
            Log.d(TAG, "开始RFID扫描");

        } catch (Exception e) {
            Log.e(TAG, "开始扫描异常", e);
            notifyStatusListeners(status -> status.onError("开始扫描失败: " + e.getMessage()));
        }
    }

    /**
     * 停止扫描
     */
    public void stopScanning() {
        if (!isScanning) {
            return;
        }

        try {
            if (commScanner != null) {
                commScanner.getRFIDScanner().close();
                commScanner.getRFIDScanner().setDataDelegate(null);
            }
            isScanning = false;

            notifyStatusListeners(status -> status.onScanningStatusChanged(false));
            Log.d(TAG, "停止RFID扫描");

        } catch (Exception e) {
            Log.e(TAG, "停止扫描异常", e);
        }
    }

    /**
     * 暂停扫描
     */
    public void pauseScanning() {
        if (isScanning) {
            stopScanning();
        }
    }

    /**
     * 恢复扫描
     */
    public void resumeScanning() {
        if (scannerConnected && !isScanning) {
            startScanning();
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        stopScanning();
        disconnect();

        dataListeners.clear();
        statusListeners.clear();

        Log.d(TAG, "RFID管理器资源已释放");
    }

    /**
     * 添加数据监听器
     */
    public void addDataListener(RfidDataListener listener) {
        if (listener != null && !dataListeners.contains(listener)) {
            dataListeners.add(listener);
        }
    }

    /**
     * 移除数据监听器
     */
    public void removeDataListener(RfidDataListener listener) {
        dataListeners.remove(listener);
    }

    /**
     * 添加状态监听器
     */
    public void addStatusListener(RfidStatusListener listener) {
        if (listener != null && !statusListeners.contains(listener)) {
            statusListeners.add(listener);
        }
    }

    /**
     * 移除状态监听器
     */
    public void removeStatusListener(RfidStatusListener listener) {
        statusListeners.remove(listener);
    }

    /**
     * 获取连接状态
     */
    public boolean isConnected() {
        return scannerConnected;
    }

    /**
     * 获取扫描状态
     */
    public boolean isScanning() {
        return isScanning;
    }

    // ========== 实现接口方法 ==========

    @Override
    public void onRFIDDataReceived(CommScanner scanner, RFIDDataReceivedEvent rfidDataReceivedEvent) {
        mainHandler.post(() -> {
            List<String> tagIds = new ArrayList<>();

            for (int i = 0; i < rfidDataReceivedEvent.getRFIDData().size(); i++) {
                StringBuilder data = new StringBuilder();
                byte[] uii = rfidDataReceivedEvent.getRFIDData().get(i).getUII();

                for (byte b : uii) {
                    data.append(String.format("%02X", b));
                }

                tagIds.add(data.toString());
            }

            // 通知所有数据监听器
            if (dataListeners != null) {
                for (RfidDataListener listener : dataListeners) {
                    if (listener != null) {
                        try {
                            listener.onRfidDataReceived(tagIds);
                        } catch (Exception e) {
                            Log.e(TAG, "通知数据监听器异常", e);
                        }
                    }
                }
            }

            Log.d(TAG, "接收到RFID数据: " + tagIds.size() + "个标签");
        });
    }

    @Override
    public void onScannerStatusChanged(CommScanner scanner, CommStatusChangedEvent state) {
        final CommConst.ScannerStatus scannerStatus = state.getStatus();
        Log.d(TAG, "扫描器状态变化: " + scannerStatus.toString());

        if (scanner == commScanner && scannerStatus.equals(CommConst.ScannerStatus.CLOSE_WAIT)) {
            mainHandler.post(() -> {
                if (scannerConnected) {
                    scannerConnected = false;
                    isScanning = false;

                    notifyStatusListeners(status -> {
                        status.onConnectionStatusChanged(false);
                        status.onScanningStatusChanged(false);
                        status.onError("设备连接已断开");
                    });

                    // 尝试自动重连
                    if (!isConnecting) {
                        Log.d(TAG, "检测到断开连接，尝试自动重连");
                        connectToScanner();
                    }
                }
            });
        }
    }

    @Override
    public void OnScannerAppeared(CommScanner scanner) {
        boolean successFlag = false;

        try {
            Log.d(TAG, "扫描器出现，开始claim");
            scanner.claim();

            CommManager.endAccept();
            CommManager.removeAcceptStatusListener(this);

            successFlag = true;

        } catch (CommException e) {
            Log.e(TAG, "扫描器claim异常", e);
        }

        try {
            setConnectedCommScanner(scanner);
            final boolean finalSuccessFlag = successFlag;

            mainHandler.post(() -> {
                isConnecting = false;

                if (finalSuccessFlag) {
                    scannerConnected = true;
                    notifyStatusListeners(status -> status.onConnectionStatusChanged(true));
                    Log.d(TAG, "RFID设备连接成功");

                    // 自动开始扫描
                    startScanning();

                } else {
                    handleConnectionFailure();
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "设置扫描器异常", e);
            mainHandler.post(() -> {
                isConnecting = false;
                handleConnectionFailure();
            });
        }
    }

    /**
     * 设置已连接的CommScanner
     */
    private void setConnectedCommScanner(CommScanner connectedCommScanner) {
        if (connectedCommScanner != null) {
            connectedCommScanner.addStatusListener(this);
        } else {
            if (commScanner != null) {
                commScanner.removeStatusListener(this);
            }
        }
        commScanner = connectedCommScanner;
    }

    /**
     * 处理连接失败
     */
    private void handleConnectionFailure() {
        if (retryCount < MAX_RETRY_COUNT) {
            retryCount++;
            Log.d(TAG, "连接失败，第" + retryCount + "次重试");

            mainHandler.postDelayed(() -> {
                try {
                    CommManager.addAcceptStatusListener(this);
                    CommManager.startAccept();
                } catch (Exception e) {
                    Log.e(TAG, "重试连接异常", e);
                }
            }, RETRY_DELAY_MS);

        } else {
            Log.e(TAG, "连接失败，已达到最大重试次数");
            notifyStatusListeners(status -> status.onError("连接设备失败，请检查设备状态"));
        }
    }

    /**
     * 通知状态监听器
     */
    private void notifyStatusListeners(StatusNotifier notifier) {
        if (statusListeners != null && notifier != null) {
            for (RfidStatusListener listener : statusListeners) {
                if (listener != null) {
                    try {
                        notifier.notify(listener);
                    } catch (Exception e) {
                        Log.e(TAG, "通知状态监听器异常", e);
                    }
                }
            }
        }
    }

    private interface StatusNotifier {
        void notify(RfidStatusListener listener);
    }
}
