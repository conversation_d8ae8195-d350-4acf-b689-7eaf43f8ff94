com.example.warehousemanagement.app-runtime-release-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06ace879758102d28a4f1ab5aa473a85\transformed\runtime-release\res
com.example.warehousemanagement.app-core-runtime-2.2.0-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08f1b241c3d0aadd668006bf5cf86eb7\transformed\core-runtime-2.2.0\res
com.example.warehousemanagement.app-ui-graphics-release-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09955d63a5c12524cf9897c542294efa\transformed\ui-graphics-release\res
com.example.warehousemanagement.app-ui-text-release-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b892bf1b5c357be5723ee23b33fcb41\transformed\ui-text-release\res
com.example.warehousemanagement.app-coordinatorlayout-1.1.0-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bb473449638126c59b02dd81dfc5e82\transformed\coordinatorlayout-1.1.0\res
com.example.warehousemanagement.app-emoji2-1.3.0-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fd6d133b4f68099e9a0be51087f77ee\transformed\emoji2-1.3.0\res
com.example.warehousemanagement.app-activity-1.8.0-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17e9d6fbd02b1f6532001d32447401d8\transformed\activity-1.8.0\res
com.example.warehousemanagement.app-lifecycle-viewmodel-release-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a4d97e35115b97b7166c47a6512594c\transformed\lifecycle-viewmodel-release\res
com.example.warehousemanagement.app-fragment-1.6.2-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fa2cbc1f3e1c8856afb02344e16735a\transformed\fragment-1.6.2\res
com.example.warehousemanagement.app-animation-core-release-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23f05609e6ea6ed25bdeb3eac7e836dd\transformed\animation-core-release\res
com.example.warehousemanagement.app-appcompat-1.6.1-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24ccdfb3f311aaa88a834c682a4cfd8c\transformed\appcompat-1.6.1\res
com.example.warehousemanagement.app-ui-tooling-release-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276436669951c331185db303038fee50\transformed\ui-tooling-release\res
com.example.warehousemanagement.app-profileinstaller-1.4.0-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bfbb9d99b076d1abf4c05a3964833f5\transformed\profileinstaller-1.4.0\res
com.example.warehousemanagement.app-ui-tooling-data-release-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ecfc0cc380d210e7f8940c367861d5b\transformed\ui-tooling-data-release\res
com.example.warehousemanagement.app-material-release-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b521a8f99434b292da05a81a19c7f8a\transformed\material-release\res
com.example.warehousemanagement.app-lifecycle-process-2.9.0-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fb91530ccf4ec3582924f10c0e4a953\transformed\lifecycle-process-2.9.0\res
com.example.warehousemanagement.app-constraintlayout-2.1.4-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b472344c1d02279c842950179cdee44\transformed\constraintlayout-2.1.4\res
com.example.warehousemanagement.app-material3-release-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5426231ab31392e5b33d42e73e9b9039\transformed\material3-release\res
com.example.warehousemanagement.app-DENSOScannerSDK-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f65664ccf018c808a64b3ab1fd6199\transformed\DENSOScannerSDK\res
com.example.warehousemanagement.app-savedstate-release-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58033aa3fe5ad325da833bd941cc5580\transformed\savedstate-release\res
com.example.warehousemanagement.app-annotation-experimental-1.4.1-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcf6071f8ae71f7f344ccc70fef4a2d\transformed\annotation-experimental-1.4.1\res
com.example.warehousemanagement.app-foundation-release-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60497f4189655a1239df256971621998\transformed\foundation-release\res
com.example.warehousemanagement.app-lifecycle-livedata-core-ktx-2.9.0-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67c50efec1c25d65ae08b1ca021dde42\transformed\lifecycle-livedata-core-ktx-2.9.0\res
com.example.warehousemanagement.app-swiperefreshlayout-1.1.0-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b3d6ddbff50fb2b51632937a346b845\transformed\swiperefreshlayout-1.1.0\res
com.example.warehousemanagement.app-lifecycle-runtime-release-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c5e0076d32ae7c88f31a85bb12e3215\transformed\lifecycle-runtime-release\res
com.example.warehousemanagement.app-material-ripple-release-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e9035f9d6f4e33d230b07a0b638c7a4\transformed\material-ripple-release\res
com.example.warehousemanagement.app-material-1.11.0-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751a317a1d40c660b2a58fb1ab3f0187\transformed\material-1.11.0\res
com.example.warehousemanagement.app-emoji2-views-helper-1.3.0-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7656c915895f1ce994b8e47353e6f7b5\transformed\emoji2-views-helper-1.3.0\res
com.example.warehousemanagement.app-ui-unit-release-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7724cca69854e32a179bdbbb5684022c\transformed\ui-unit-release\res
com.example.warehousemanagement.app-ui-release-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78b878a2477ffed38acd39b519b92118\transformed\ui-release\res
com.example.warehousemanagement.app-ui-test-manifest-1.6.6-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d484dbbbc102e0e00b4a720c4b7b9c\transformed\ui-test-manifest-1.6.6\res
com.example.warehousemanagement.app-lifecycle-viewmodel-ktx-2.9.0-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8247a4d041b3da5690fdb6b6e86f06b0\transformed\lifecycle-viewmodel-ktx-2.9.0\res
com.example.warehousemanagement.app-appcompat-resources-1.6.1-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83486373376e1c239e8f4fb78503c430\transformed\appcompat-resources-1.6.1\res
com.example.warehousemanagement.app-lifecycle-livedata-core-2.9.0-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eb39b49ba5be68e6649c6773d11067\transformed\lifecycle-livedata-core-2.9.0\res
com.example.warehousemanagement.app-ui-tooling-preview-release-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b1c7ec8210b084c434d494ebe34ba35\transformed\ui-tooling-preview-release\res
com.example.warehousemanagement.app-material-icons-core-release-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e1a7cf70cd8cd87f634208a3f6bd915\transformed\material-icons-core-release\res
com.example.warehousemanagement.app-databinding-adapters-8.8.0-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90e344e85c584f113ea612d265ccbd7c\transformed\databinding-adapters-8.8.0\res
com.example.warehousemanagement.app-core-ktx-1.16.0-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91deca6799a1cf6745630877ddaaea3d\transformed\core-ktx-1.16.0\res
com.example.warehousemanagement.app-cardview-1.0.0-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9577bcd0e8f7d8a3b1dd74d6f41579ab\transformed\cardview-1.0.0\res
com.example.warehousemanagement.app-viewpager2-1.1.0-beta02-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fa87bea8d3064991c4950a7cd31f392\transformed\viewpager2-1.1.0-beta02\res
com.example.warehousemanagement.app-lifecycle-service-2.9.0-40 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a544610fc6f3b681623a4482e3dff666\transformed\lifecycle-service-2.9.0\res
com.example.warehousemanagement.app-animation-release-41 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b122ad9efa6235fdb3f87bd08421739d\transformed\animation-release\res
com.example.warehousemanagement.app-core-viewtree-1.0.0-42 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1eec52a1e549dbbf8703d25e29e508b\transformed\core-viewtree-1.0.0\res
com.example.warehousemanagement.app-ui-geometry-release-43 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2338b0d2fd806bb6b95f92bddd790be\transformed\ui-geometry-release\res
com.example.warehousemanagement.app-lifecycle-viewmodel-savedstate-release-44 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3a257e4564684bb9a1a45d3213fa2d1\transformed\lifecycle-viewmodel-savedstate-release\res
com.example.warehousemanagement.app-drawerlayout-1.1.1-45 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1b135366676b31e1296cde23c82c3b4\transformed\drawerlayout-1.1.1\res
com.example.warehousemanagement.app-core-1.16.0-46 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34cc1cda6217c045c9702ce14b9b574\transformed\core-1.16.0\res
com.example.warehousemanagement.app-savedstate-ktx-1.3.0-47 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c54f0fb8e46aebdab17481f77571f1d5\transformed\savedstate-ktx-1.3.0\res
com.example.warehousemanagement.app-recyclerview-1.3.2-48 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c902c3c9a42f60cb68e868a4de7e97ec\transformed\recyclerview-1.3.2\res
com.example.warehousemanagement.app-customview-poolingcontainer-1.0.0-49 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9c9192828977ce370dde14435a3dd64\transformed\customview-poolingcontainer-1.0.0\res
com.example.warehousemanagement.app-transition-1.2.0-50 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d14e6ace58077bebd3821a913a07c5f4\transformed\transition-1.2.0\res
com.example.warehousemanagement.app-ui-util-release-51 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3c20c358e94118bee9b4161e561eb71\transformed\ui-util-release\res
com.example.warehousemanagement.app-activity-compose-1.8.0-52 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d65dda3679d2a5857597b84739043fdd\transformed\activity-compose-1.8.0\res
com.example.warehousemanagement.app-startup-runtime-1.1.1-53 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9ecf7b060063059954dbd975563108b\transformed\startup-runtime-1.1.1\res
com.example.warehousemanagement.app-foundation-layout-release-54 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dac543713abd27cc006a85d011382eca\transformed\foundation-layout-release\res
com.example.warehousemanagement.app-runtime-saveable-release-55 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbc11017a7b8e97b301617e73ce9a8ca\transformed\runtime-saveable-release\res
com.example.warehousemanagement.app-databinding-runtime-8.8.0-56 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f405dc4c4a2e7cb5f834d6576522c6b1\transformed\databinding-runtime-8.8.0\res
com.example.warehousemanagement.app-tracing-1.2.0-57 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f62fe76726d959afc2189ecef896076c\transformed\tracing-1.2.0\res
com.example.warehousemanagement.app-lifecycle-livedata-2.9.0-58 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f795e9a007962968e1e605d54e80d471\transformed\lifecycle-livedata-2.9.0\res
com.example.warehousemanagement.app-activity-ktx-1.8.0-59 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa6b54dbb4dabb4ce6cbe6398c571b63\transformed\activity-ktx-1.8.0\res
com.example.warehousemanagement.app-pngs-60 C:\Users\<USER>\Desktop\Box\app\build\generated\res\pngs\debug
com.example.warehousemanagement.app-resValues-61 C:\Users\<USER>\Desktop\Box\app\build\generated\res\resValues\debug
com.example.warehousemanagement.app-packageDebugResources-62 C:\Users\<USER>\Desktop\Box\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.warehousemanagement.app-packageDebugResources-63 C:\Users\<USER>\Desktop\Box\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.warehousemanagement.app-debug-64 C:\Users\<USER>\Desktop\Box\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.warehousemanagement.app-debug-65 C:\Users\<USER>\Desktop\Box\app\src\debug\res
com.example.warehousemanagement.app-main-66 C:\Users\<USER>\Desktop\Box\app\src\main\res
