<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.WarehouseManagement" parent="Theme.Material3.DayNight">
        <!-- Customize your light theme here. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
    </style>

    <style name="Theme.WarehouseManagement" parent="Base.Theme.WarehouseManagement" />
    
    <!-- 登录主题 -->
    <style name="LoginTheme" parent="Theme.WarehouseManagement">
        <item name="android:windowBackground">@color/primary_light</item>
    </style>
    
    <!-- 按钮样式 -->
    <style name="PrimaryButton" parent="Widget.Material3.Button">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textColor">@color/text_white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="backgroundTint">@color/button_primary</item>
    </style>
    
    <style name="SecondaryButton" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textColor">@color/button_secondary</item>
        <item name="android:textSize">16sp</item>
        <item name="strokeColor">@color/button_secondary</item>
    </style>
    
    <!-- 输入框样式 -->
    <style name="EditTextStyle" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">16dp</item>
        <item name="boxStrokeColor">@color/edit_text_border_focused</item>
        <item name="hintTextColor">@color/text_hint</item>
    </style>
    
    <!-- 卡片样式 -->
    <style name="CardStyle" parent="Widget.Material3.CardView.Elevated">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">8dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="cardBackgroundColor">@color/card_background</item>
    </style>
    
    <!-- 标题样式 -->
    <style name="TitleTextStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>
    
    <!-- 副标题样式 -->
    <style name="SubtitleTextStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>
    
    <!-- 内容文本样式 -->
    <style name="BodyTextStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <!-- Text Styles -->
    <style name="SubtitleText">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style>

    <style name="BodyText">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:layout_marginBottom">4dp</item>
    </style>

    <style name="TitleText">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_marginBottom">12dp</item>
    </style>
</resources>