{"logs": [{"outputFile": "com.example.warehousemanagement.app-mergeDebugResources-62:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c34cc1cda6217c045c9702ce14b9b574\\transformed\\core-1.16.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "38,39,40,41,42,43,44,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3386,3482,3584,3683,3782,3886,3989,16272", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3477,3579,3678,3777,3881,3984,4100,16368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\60497f4189655a1239df256971621998\\transformed\\foundation-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "183,184", "startColumns": "4,4", "startOffsets": "16637,16723", "endColumns": "85,84", "endOffsets": "16718,16803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78b878a2477ffed38acd39b519b92118\\transformed\\ui-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,963,1027,1105,1187,1260,1337,1403,1524"}, "to": {"startLines": "48,49,50,51,52,56,57,170,171,172,173,175,176,178,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4397,4489,4571,4665,4764,5073,5155,15576,15665,15749,15814,15956,16034,16199,16373,16450,16516", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "4484,4566,4660,4759,4846,5150,5239,15660,15744,15809,15873,16029,16111,16267,16445,16511,16632"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5426231ab31392e5b33d42e73e9b9039\\transformed\\material3-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,400,514,613,708,820,956,1072,1208,1292,1391,1482,1579,1698,1823,1927,2054,2177,2305,2466,2587,2703,2826,2951,3043,3141,3258,3382,3479,3581,3683,3813,3952,4058,4157,4235,4331,4425,4512,4599,4701,4783,4867,4968,5069,5169,5268,5356,5462,5563,5667,5787,5869,5969", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "168,284,395,509,608,703,815,951,1067,1203,1287,1386,1477,1574,1693,1818,1922,2049,2172,2300,2461,2582,2698,2821,2946,3038,3136,3253,3377,3474,3576,3678,3808,3947,4053,4152,4230,4326,4420,4507,4594,4696,4778,4862,4963,5064,5164,5263,5351,5457,5558,5662,5782,5864,5964,6059"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5303,5421,5537,5648,5762,5861,5956,6068,6204,6320,6456,6540,6639,6730,6827,6946,7071,7175,7302,7425,7553,7714,7835,7951,8074,8199,8291,8389,8506,8630,8727,8829,8931,9061,9200,9306,9405,9483,9579,9673,9760,9847,9949,10031,10115,10216,10317,10417,10516,10604,10710,10811,10915,11035,11117,11217", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "5416,5532,5643,5757,5856,5951,6063,6199,6315,6451,6535,6634,6725,6822,6941,7066,7170,7297,7420,7548,7709,7830,7946,8069,8194,8286,8384,8501,8625,8722,8824,8926,9056,9195,9301,9400,9478,9574,9668,9755,9842,9944,10026,10110,10211,10312,10412,10511,10599,10705,10806,10910,11030,11112,11212,11307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\751a317a1d40c660b2a58fb1ab3f0187\\transformed\\material-1.11.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,777,892,971,1036,1126,1193,1252,1342,1406,1470,1533,1602,1666,1720,1832,1890,1952,2006,2078,2200,2287,2368,2508,2585,2666,2793,2884,2961,3015,3066,3132,3202,3279,3366,3441,3512,3589,3658,3727,3834,3925,3997,4086,4175,4249,4321,4407,4457,4536,4602,4682,4766,4828,4892,4955,5024,5124,5219,5311,5403,5461,5516", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,81,77,76,85,83,97,114,78,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,80,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77", "endOffsets": "267,349,427,504,590,674,772,887,966,1031,1121,1188,1247,1337,1401,1465,1528,1597,1661,1715,1827,1885,1947,2001,2073,2195,2282,2363,2503,2580,2661,2788,2879,2956,3010,3061,3127,3197,3274,3361,3436,3507,3584,3653,3722,3829,3920,3992,4081,4170,4244,4316,4402,4452,4531,4597,4677,4761,4823,4887,4950,5019,5119,5214,5306,5398,5456,5511,5589"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,58,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2979,3061,3139,3216,3302,4105,4203,4318,4851,4916,5006,5244,11312,11402,11466,11530,11593,11662,11726,11780,11892,11950,12012,12066,12138,12260,12347,12428,12568,12645,12726,12853,12944,13021,13075,13126,13192,13262,13339,13426,13501,13572,13649,13718,13787,13894,13985,14057,14146,14235,14309,14381,14467,14517,14596,14662,14742,14826,14888,14952,15015,15084,15184,15279,15371,15463,15521,15878", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,58,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,174", "endColumns": "12,81,77,76,85,83,97,114,78,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,80,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77", "endOffsets": "317,3056,3134,3211,3297,3381,4198,4313,4392,4911,5001,5068,5298,11397,11461,11525,11588,11657,11721,11775,11887,11945,12007,12061,12133,12255,12342,12423,12563,12640,12721,12848,12939,13016,13070,13121,13187,13257,13334,13421,13496,13567,13644,13713,13782,13889,13980,14052,14141,14230,14304,14376,14462,14512,14591,14657,14737,14821,14883,14947,15010,15079,15179,15274,15366,15458,15516,15571,15951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\24ccdfb3f311aaa88a834c682a4cfd8c\\transformed\\appcompat-1.6.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,426,526,634,718,818,933,1011,1086,1177,1270,1365,1459,1559,1652,1747,1841,1932,2023,2105,2208,2311,2410,2515,2619,2723,2879,16116", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "421,521,629,713,813,928,1006,1081,1172,1265,1360,1454,1554,1647,1742,1836,1927,2018,2100,2203,2306,2405,2510,2614,2718,2874,2974,16194"}}]}]}