{"logs": [{"outputFile": "com.example.warehousemanagement.app-mergeDebugResources-62:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78b878a2477ffed38acd39b519b92118\\transformed\\ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,989,1059,1139,1224,1297,1376,1446", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,984,1054,1134,1219,1292,1371,1441,1559"}, "to": {"startLines": "49,50,51,52,53,57,58,171,172,173,174,176,177,179,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4563,4660,4747,4844,4945,5257,5334,16040,16132,16217,16288,16438,16518,16690,16864,16943,17013", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "4655,4742,4839,4940,5026,5329,5420,16127,16212,16283,16353,16513,16598,16758,16938,17008,17126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\60497f4189655a1239df256971621998\\transformed\\foundation-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,89", "endOffsets": "138,228"}, "to": {"startLines": "184,185", "startColumns": "4,4", "startOffsets": "17131,17219", "endColumns": "87,89", "endOffsets": "17214,17304"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c34cc1cda6217c045c9702ce14b9b574\\transformed\\core-1.16.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "39,40,41,42,43,44,45,180", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3531,3629,3731,3828,3932,4036,4141,16763", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3624,3726,3823,3927,4031,4136,4252,16859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\751a317a1d40c660b2a58fb1ab3f0187\\transformed\\material-1.11.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,397,473,553,660,753,847,978,1059,1125,1217,1285,1348,1451,1511,1577,1633,1704,1764,1818,1930,1987,2048,2102,2178,2303,2389,2472,2610,2691,2774,2905,2993,3071,3125,3181,3247,3321,3399,3488,3570,3645,3721,3796,3867,3974,4064,4137,4229,4325,4397,4473,4569,4622,4704,4771,4858,4945,5007,5071,5134,5203,5308,5418,5514,5622,5680,5740", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,75,75,79,106,92,93,130,80,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,82,137,80,82,130,87,77,53,55,65,73,77,88,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "316,392,468,548,655,748,842,973,1054,1120,1212,1280,1343,1446,1506,1572,1628,1699,1759,1813,1925,1982,2043,2097,2173,2298,2384,2467,2605,2686,2769,2900,2988,3066,3120,3176,3242,3316,3394,3483,3565,3640,3716,3791,3862,3969,4059,4132,4224,4320,4392,4468,4564,4617,4699,4766,4853,4940,5002,5066,5129,5198,5303,5413,5509,5617,5675,5735,5815"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,54,55,56,59,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3099,3175,3251,3331,3438,4257,4351,4482,5031,5097,5189,5425,11648,11751,11811,11877,11933,12004,12064,12118,12230,12287,12348,12402,12478,12603,12689,12772,12910,12991,13074,13205,13293,13371,13425,13481,13547,13621,13699,13788,13870,13945,14021,14096,14167,14274,14364,14437,14529,14625,14697,14773,14869,14922,15004,15071,15158,15245,15307,15371,15434,15503,15608,15718,15814,15922,15980,16358", "endLines": "6,34,35,36,37,38,46,47,48,54,55,56,59,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,175", "endColumns": "12,75,75,79,106,92,93,130,80,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,82,137,80,82,130,87,77,53,55,65,73,77,88,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "366,3170,3246,3326,3433,3526,4346,4477,4558,5092,5184,5252,5483,11746,11806,11872,11928,11999,12059,12113,12225,12282,12343,12397,12473,12598,12684,12767,12905,12986,13069,13200,13288,13366,13420,13476,13542,13616,13694,13783,13865,13940,14016,14091,14162,14269,14359,14432,14524,14620,14692,14768,14864,14917,14999,15066,15153,15240,15302,15366,15429,15498,15603,15713,15809,15917,15975,16035,16433"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\24ccdfb3f311aaa88a834c682a4cfd8c\\transformed\\appcompat-1.6.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,478,579,685,771,875,997,1081,1162,1253,1346,1441,1535,1635,1728,1823,1928,2019,2110,2196,2301,2407,2510,2616,2725,2832,3002,16603", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "473,574,680,766,870,992,1076,1157,1248,1341,1436,1530,1630,1723,1818,1923,2014,2105,2191,2296,2402,2505,2611,2720,2827,2997,3094,16685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5426231ab31392e5b33d42e73e9b9039\\transformed\\material3-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,287,414,530,628,722,833,969,1088,1230,1315,1415,1510,1608,1724,1849,1954,2095,2235,2368,2548,2673,2793,2918,3040,3136,3234,3351,3481,3581,3683,3792,3934,4083,4192,4295,4372,4470,4568,4657,4743,4850,4930,5013,5110,5213,5306,5404,5491,5599,5696,5798,5931,6011,6118", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "166,282,409,525,623,717,828,964,1083,1225,1310,1410,1505,1603,1719,1844,1949,2090,2230,2363,2543,2668,2788,2913,3035,3131,3229,3346,3476,3576,3678,3787,3929,4078,4187,4290,4367,4465,4563,4652,4738,4845,4925,5008,5105,5208,5301,5399,5486,5594,5691,5793,5926,6006,6113,6210"}, "to": {"startLines": "60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5488,5604,5720,5847,5963,6061,6155,6266,6402,6521,6663,6748,6848,6943,7041,7157,7282,7387,7528,7668,7801,7981,8106,8226,8351,8473,8569,8667,8784,8914,9014,9116,9225,9367,9516,9625,9728,9805,9903,10001,10090,10176,10283,10363,10446,10543,10646,10739,10837,10924,11032,11129,11231,11364,11444,11551", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "5599,5715,5842,5958,6056,6150,6261,6397,6516,6658,6743,6843,6938,7036,7152,7277,7382,7523,7663,7796,7976,8101,8221,8346,8468,8564,8662,8779,8909,9009,9111,9220,9362,9511,9620,9723,9800,9898,9996,10085,10171,10278,10358,10441,10538,10641,10734,10832,10919,11027,11124,11226,11359,11439,11546,11643"}}]}]}