<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background">

    <!-- 顶部工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="@string/recovery_add"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- RFID扫描区域 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/CardStyle"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        style="@style/SubtitleText"
                        android:text="@string/rfid_scan_area"
                        android:layout_marginBottom="12dp" />

                    <!-- 连接状态 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            style="@style/BodyText"
                            android:text="@string/connection_status"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/tv_connection_status"
                            style="@style/BodyText"
                            android:text="@string/disconnected"
                            android:textColor="@color/error" />

                    </LinearLayout>

                    <!-- 扫描按钮 -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_scan"
                        style="@style/PrimaryButton"
                        android:text="@string/start_scan"
                        android:layout_height="48dp"
                        android:enabled="false" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 已扫描标签列表 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/CardStyle"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        style="@style/SubtitleText"
                        android:text="@string/scanned_tags"
                        android:layout_marginBottom="12dp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_scanned_tags"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minHeight="100dp" />

                    <!-- 空状态提示 -->
                    <TextView
                        android:id="@+id/tv_empty_tags"
                        style="@style/BodyText"
                        android:text="@string/no_scanned_tags"
                        android:textColor="@color/text_secondary"
                        android:gravity="center"
                        android:layout_height="100dp"
                        android:visibility="visible" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 回收信息 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/CardStyle">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        style="@style/SubtitleText"
                        android:text="@string/recovery_info"
                        android:layout_marginBottom="16dp" />

                    <!-- 客户名称 -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/EditTextStyle"
                        android:hint="@string/customer_name">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_customer_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 回收日期 -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/EditTextStyle"
                        android:hint="@string/recovery_date">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_recovery_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:focusable="false"
                            android:clickable="true" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 备注1 -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/EditTextStyle"
                        android:hint="@string/remark1">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_remark1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 备注2 -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/EditTextStyle"
                        android:hint="@string/remark2"
                        android:layout_marginBottom="0dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_remark2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

    <!-- 底部按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/surface">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            style="@style/SecondaryButton"
            android:text="@string/cancel"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            style="@style/PrimaryButton"
            android:text="@string/save"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>