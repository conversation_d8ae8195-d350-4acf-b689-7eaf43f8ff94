package densowave.com.DensoScannerSDK_demo;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Build;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.os.Bundle;
import android.os.Handler;
import androidx.appcompat.app.AppCompatActivity;
import android.view.Gravity;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;
import android.view.KeyEvent;

import com.densowave.scannersdk.Dto.RFIDScannerSettings;

import com.densowave.scannersdk.Common.CommException;
import com.densowave.scannersdk.Common.CommManager;
import com.densowave.scannersdk.Common.CommScanner;
import com.densowave.scannersdk.Common.CommStatusChangedEvent;
import com.densowave.scannersdk.Const.CommConst;
import com.densowave.scannersdk.Listener.RFIDDataDelegate;
import com.densowave.scannersdk.Listener.ScannerAcceptStatusListener;
import com.densowave.scannersdk.Listener.ScannerStatusListener;
import com.densowave.scannersdk.RFID.RFIDDataReceivedEvent;

// 导入正确的R类
import com.example.warehousemanagement.R;

import java.util.ArrayList;
import java.util.List;

import androidx.viewpager2.widget.ViewPager2;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

/**
 * 简化版RFID扫描Activity
 * 功能：
 * 1. 显示设备连接状态
 * 2. 自动开启/关闭扫描
 * 3. 在TextView中显示扫描结果，用逗号分隔
 * 4. 每次扫描清空上次结果
 */
public class RfidWriteActivity extends AppCompatActivity implements RFIDDataDelegate, ScannerStatusListener, ScannerAcceptStatusListener {

    // 当前活动的静态引用，用于静态方法中访问
    private static RfidWriteActivity currentActivity;

    private TextView tvConnectionStatus; // 设备连接状态标签
    private Button btnConnect;           // 连接设备按钮
    private EditText etWriteContent;     // 写入内容输入框
    private Button btnWriteTag;          // 写入标签按钮
    private Handler handler = new Handler();
    private boolean scannerConnectedOnCreate = false;
    private RFIDReadWriteHelper rfidReadWriteHelper; // RFID读写帮助类

    // 从BaseActivity移植过来的变量
    public static CommScanner commScanner;
    public static boolean scannerConnected = false;
    private Toast ts = null;
    private boolean topActivity = false;

    // 定义错误消息字符串常量，替代R.string引用
    private static final String MSG_COMMUNICATION_ERROR = "通信错误，请检查设备连接";
    private static final String MSG_NO_CONNECTION = "设备未连接，请先连接设备";
    private static final String MSG_CONNECTING = "正在连接设备，请稍候...";
    private static final String MSG_CONNECT_SUCCESS = "设备连接成功";
    private static final String MSG_CONNECT_FAILED = "设备连接失败，请重试";

    // 连接重试相关变量
    private Handler retryHandler = new Handler();
    private int retryCount = 0;
    private static final int MAX_RETRY_COUNT = 3;
    private static final int RETRY_DELAY_MS = 2000; // 2秒后重试
    private boolean isConnecting = false;

    // Tab相关组件
    private TabLayout tabLayout;
    private ViewPager2 viewPager;
    private TabAdapter tabAdapter;
    private String[] tabTitles = {"扫描结果", "日志"};

    // 权限请求相关常量
    private static final int PERMISSION_REQUEST_CODE = 100;
    private static final String[] REQUIRED_PERMISSIONS = {
            Manifest.permission.BLUETOOTH,
            Manifest.permission.BLUETOOTH_ADMIN,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
    };

    // Android 12及以上需要的额外权限
    private static final String[] REQUIRED_PERMISSIONS_S = {
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.BLUETOOTH_SCAN,
            Manifest.permission.BLUETOOTH_ADVERTISE
    };

    private StringBuilder results = new StringBuilder();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 设置当前活动实例引用
        currentActivity = this;
        setContentView(R.layout.activity_simple_rfid);

        // 初始化UI组件
        tvConnectionStatus = findViewById(R.id.tv_connection_status);
        btnConnect = findViewById(R.id.btn_connect);
        etWriteContent = findViewById(R.id.et_write_content);
        btnWriteTag = findViewById(R.id.btn_write_tag);
        
        // 设置写入按钮点击事件
        btnWriteTag.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                writeToTag();
            }
        });

        // 初始化Tab组件
        tabLayout = findViewById(R.id.tab_layout);
        viewPager = findViewById(R.id.view_pager);
        setupTabs();

        // 确保Tab初始化完成后再记录日志
        logMessage("应用启动");

        // 检查并请求必要权限
        if (!checkAndRequestPermissions()) {
            logMessage("正在请求必要权限...");
        }

        // 设置连接按钮点击事件
        btnConnect.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    // 再次检查权限
                    if (!hasRequiredPermissions()) {
                        showMessage("缺少必要权限，请授予蓝牙和位置权限");
                        logMessage("缺少必要权限，无法连接设备");
                        checkAndRequestPermissions();
                        return;
                    }

                    if (!scannerConnected && !isConnecting) {
                        // 如果未连接且不在连接过程中，则开始连接
                        logMessage("开始连接过程...");
                        connectToScanner();
                    } else if (scannerConnected) {
                        // 如果已连接，则断开连接
                        logMessage("开始断开连接...");
                        disconnectCommScanner();
                        updateConnectionStatus();
                        btnConnect.setText("连接设备");
                        logMessage("断开连接完成");
                    }
                } catch (Exception e) {
                    // 捕获并显示按钮点击过程中的任何异常
                    String errorMsg = "按钮点击异常: " + e.getClass().getName() + ": " + e.getMessage();
                    logMessage(errorMsg);
                    e.printStackTrace();
                }
            }
        });

        // 检查设备连接状态
        scannerConnectedOnCreate = isCommScanner();
        updateConnectionStatus();

        // 如果设备已连接，设置数据委托并开始扫描
        if (scannerConnectedOnCreate) {
            try {
                getCommScanner().getRFIDScanner().setDataDelegate(this);
                startRfidScan();
                btnConnect.setText("断开连接");
                // 初始化RFID读写帮助类
                rfidReadWriteHelper = new RFIDReadWriteHelper(getCommScanner());
            } catch (Exception e) {
                showMessage(MSG_COMMUNICATION_ERROR);
            }
        } else {
            showMessage(MSG_NO_CONNECTION);
            btnConnect.setText("连接设备");
        }
    }

    /**
     * 更新连接状态显示
     */
    private void updateConnectionStatus() {
        if (scannerConnectedOnCreate) {
            tvConnectionStatus.setText("设备状态: 已连接");
            btnConnect.setText("断开连接");
        } else {
            tvConnectionStatus.setText("设备状态: 未连接");
            btnConnect.setText("连接设备");
        }
    }

    /**
     * 连接到扫描器设备
     */
    private void connectToScanner() {
        try {
            isConnecting = true;
            retryCount = 0;
            showMessage(MSG_CONNECTING);
            logMessage("状态: 正在连接...");
            tvConnectionStatus.setText("设备状态: 正在连接...");
            btnConnect.setEnabled(false);

            // 使用与MainActivity相同的方式连接扫描器
            logMessage("添加监听器并开始接受连接...");
            CommManager.addAcceptStatusListener(this);
            CommManager.startAccept();
            logMessage("连接请求已发送");
        } catch (Exception e) {
            String errorMsg = "连接过程异常: " + e.getClass().getName() + ": " + e.getMessage();
            logMessage(errorMsg);
            isConnecting = false;
            btnConnect.setEnabled(true);
            e.printStackTrace();
        }
    }

    /**
     * 处理连接失败的情况，实现重试机制
     */
    private void handleConnectionFailure() {
        try {
            if (retryCount < MAX_RETRY_COUNT) {
                retryCount++;
                String retryMsg = MSG_CONNECT_FAILED + "，第" + retryCount + "次重试...";
                showMessage(retryMsg);
                logMessage(retryMsg);

                // 延迟一段时间后重试
                retryHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            // 使用与MainActivity相同的方式连接扫描器
                            logMessage("重试连接: 添加监听器并开始接受连接...");
                            CommManager.addAcceptStatusListener(RfidWriteActivity.this);
                            CommManager.startAccept();
                            logMessage("重试连接请求已发送");
                        } catch (Exception e) {
                            String errorMsg = "重试连接异常: " + e.getClass().getName() + ": " + e.getMessage();
                            logMessage(errorMsg);
                            e.printStackTrace();
                        }
                    }
                }, RETRY_DELAY_MS);
            } else {
                isConnecting = false;
                String failMsg = MSG_CONNECT_FAILED + "，请检查设备并重试";
                showMessage(failMsg);
                logMessage(failMsg);
                tvConnectionStatus.setText("设备状态: 未连接");
                btnConnect.setText("连接设备");
                btnConnect.setEnabled(true);
            }
        } catch (Exception e) {
            String errorMsg = "处理连接失败异常: " + e.getClass().getName() + ": " + e.getMessage();
            logMessage(errorMsg);
            isConnecting = false;
            btnConnect.setEnabled(true);
            e.printStackTrace();
        }
    }

    /**
     * 开始RFID扫描
     */
    private void startRfidScan() {
        if (scannerConnectedOnCreate) {
            try {
                // 清空上次扫描结果
                if (tabAdapter != null && tabAdapter.getScanResultsFragment() != null) {
                    tabAdapter.getScanResultsFragment().updateScanResults("");
                }
                // 开始扫描
                getCommScanner().getRFIDScanner().openInventory();
            } catch (Exception e) {
                showMessage(MSG_COMMUNICATION_ERROR);
                e.printStackTrace();
            }
        }
    }

    /**
     * 停止RFID扫描
     */
    private void stopRfidScan() {
        if (scannerConnectedOnCreate) {
            try {
                getCommScanner().getRFIDScanner().close();
            } catch (Exception e) {
                showMessage(MSG_COMMUNICATION_ERROR);
                e.printStackTrace();
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();

        // 中止连接请求
        CommManager.endAccept();
        CommManager.removeAcceptStatusListener(this);
    }

    @Override
    protected void onDestroy() {
        // 停止扫描并移除数据委托
        if (scannerConnectedOnCreate) {
            stopRfidScan();
            getCommScanner().getRFIDScanner().setDataDelegate(null);
        }

        // 释放RFID读写帮助类资源
        if (rfidReadWriteHelper != null) {
            rfidReadWriteHelper.release();
            rfidReadWriteHelper = null;
            logMessage("RFID读写帮助类已释放");
        }

        // 清理Handler
        handler = null;
        retryHandler.removeCallbacksAndMessages(null);
        retryHandler = null;

        // 中止连接请求
        CommManager.endAccept();
        CommManager.removeAcceptStatusListener(this);
        
        // 清除当前活动实例引用
        if (currentActivity == this) {
            currentActivity = null;
        }

        super.onDestroy();
    }

    /**
     * 处理返回键事件
     */
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            // 停止扫描并移除数据委托
            if (scannerConnectedOnCreate) {
                stopRfidScan();
                getCommScanner().getRFIDScanner().setDataDelegate(null);
            }
            handler = null;
            finish();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 处理RFID数据接收事件
     */
    @Override
    public void onRFIDDataReceived(CommScanner scanner, final RFIDDataReceivedEvent rfidDataReceivedEvent) {
        // 在UI线程中处理数据
        handler.post(new Runnable() {
            @Override
            public void run() {
                // 处理接收到的数据

                for (int i = 0; i < rfidDataReceivedEvent.getRFIDData().size(); i++) {
                    String data = "";
                    byte[] uii = rfidDataReceivedEvent.getRFIDData().get(i).getUII();
                    for (int loop = 0; loop < uii.length; loop++) {
                        data += String.format("%02X ", uii[loop]).trim();
                    }

                    // 添加逗号分隔
                    if (results.length() > 0) {
                        results.append(", ");
                    }
                    results.append(data);
                }

                // 显示结果
                if (tabAdapter != null && tabAdapter.getScanResultsFragment() != null) {
                    tabAdapter.getScanResultsFragment().updateScanResults(results.toString());
                }

                // 记录日志
                logMessage("接收到RFID数据: " + results.toString());
            }
        });
    }

    /**
     * 设置已连接的CommScanner
     * @param connectedCommScanner 设置已连接的CommScanner，如果为null，则将持有的CommScanner设为null
     */
    public void setConnectedCommScanner(CommScanner connectedCommScanner) {
        if (connectedCommScanner != null) {
            scannerConnected = true;
            connectedCommScanner.addStatusListener(this);
        } else {
            scannerConnected = false;
            if (commScanner != null) {
                commScanner.removeStatusListener(this);
            }
        }
        commScanner = connectedCommScanner;
    }

    /**
     * 获取CommScanner
     * 由于即使获取的CommScanner不为null也不一定总是连接的，
     * 使用isCommScanner来检查扫描器是否连接
     * @return CommScanner实例
     */
    public CommScanner getCommScanner() {
        return commScanner;
    }

    /**
     * 判断CommScanner
     * @return 如果CommScanner已连接返回true，否则返回false
     */
    public boolean isCommScanner() {
        return scannerConnected;
    }

    /**
     * 断开SP1连接
     */
    public void disconnectCommScanner() {
        if (commScanner != null) {
            try {
                commScanner.close();
                commScanner.removeStatusListener(this);
                scannerConnected = false;
                commScanner = null;
            } catch (CommException e) {
                this.showMessage(e.getMessage());
            }
        }
    }

    /**
     * 设置Tab布局
     */
    private void setupTabs() {
        try {
            // 创建TabAdapter
            tabAdapter = new TabAdapter(this);
            viewPager.setAdapter(tabAdapter);

            // 连接TabLayout和ViewPager2
            new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
                tab.setText(tabTitles[position]);
            }).attach();

            System.out.println("Tab设置完成");

            // 预加载所有页面，确保Fragment立即创建
            viewPager.setOffscreenPageLimit(tabTitles.length);

            // 添加页面切换监听器，确保在切换页面时能正确显示内容
            viewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
                @Override
                public void onPageSelected(int position) {
                    super.onPageSelected(position);
                    System.out.println("页面已选择: " + position);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("设置Tab异常: " + e.getMessage());
        }
    }

    /**
     * 记录消息到错误详情Fragment和日志文件
     * @param message 要记录的消息
     */
    public static void logMessage(final String message) {
        // 获取时间戳
        String timestamp = new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date());
        String logMsg = timestamp + ": " + message;
        
        // 写入日志到文件
        writeLogToFile(logMsg);
        
        // 获取当前活动的实例
        final RfidWriteActivity activity = RfidWriteActivity.currentActivity;
        if (activity == null) {
            // 如果没有活动实例，直接输出到控制台
            System.out.println("[日志] " + message);
            return;
        }
        
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                try {
                    if (activity.tabAdapter != null) {
                        ErrorDetailsFragment errorFragment = activity.tabAdapter.getErrorDetailsFragment();
                        if (errorFragment != null) {
                            errorFragment.appendErrorLog(message);
                        } else {
                            // 如果Fragment为null，直接输出到控制台
                            System.out.println("错误日志Fragment为null: " + message);
                        }
                    } else {
                        // 如果tabAdapter为null，直接输出到控制台
                        System.out.println("tabAdapter为null: " + message);
                    }
                } catch (Exception e) {
                    // 捕获并打印任何异常
                    e.printStackTrace();
                    System.out.println("记录消息异常: " + e.getMessage() + ", 原始消息: " + message);
                }
            }
        });
    }
    
    /**
     * 将日志写入文件
     * @param logMsg 日志消息
     */
    private static void writeLogToFile(String logMsg) {
        try {
            java.io.File logDir;
            java.io.File logFile;
            
            // 尝试获取当前活动的实例
            RfidWriteActivity activity = RfidWriteActivity.currentActivity;
            
            if (activity != null && isExternalStorageWritable() && hasStoragePermission(activity)) {
                // 使用外部存储
                java.io.File externalDir = android.os.Environment.getExternalStorageDirectory();
                logDir = new java.io.File(externalDir, "RfidAppLogs");
            } else {
                // 使用内部存储（应用私有目录）
                if (activity != null) {
                    logDir = new java.io.File(activity.getFilesDir(), "RfidAppLogs");
                } else {
                    // 无法获取任何存储位置
                    System.out.println("无法获取存储位置，日志未写入文件");
                    return;
                }
            }
            
            // 确保目录存在
            if (!logDir.exists()) {
                logDir.mkdirs();
            }
            
            // 创建日志文件（按日期命名）
            String date = new java.text.SimpleDateFormat("yyyy-MM-dd").format(new java.util.Date());
            logFile = new java.io.File(logDir, "rfid_log_" + date + ".txt");
            
            // 追加写入日志
            java.io.FileWriter writer = new java.io.FileWriter(logFile, true);
            writer.append(logMsg).append("\n");
            writer.flush();
            writer.close();
            
            // 输出日志文件路径（便于查找）
            System.out.println("日志已写入: " + logFile.getAbsolutePath());
        } catch (Exception e) {
            System.out.println("写入日志文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查外部存储是否可写
     */
    private static boolean isExternalStorageWritable() {
        String state = android.os.Environment.getExternalStorageState();
        return android.os.Environment.MEDIA_MOUNTED.equals(state);
    }
    
    /**
     * 检查是否有存储权限
     */
    private static boolean hasStoragePermission(android.content.Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return (ContextCompat.checkSelfPermission(context, Manifest.permission.WRITE_EXTERNAL_STORAGE) 
                    == PackageManager.PERMISSION_GRANTED);
        }
        return true; // 低于Android 6.0的设备安装时已授予权限
    }

    /**
     * 显示Toast消息并记录到日志
     * @param msg 消息内容
     */
    public synchronized void showMessage(String msg) {
        logMessage("Toast: " + msg);
        if (ts != null) {
            ts.cancel();
        }
        ts = Toast.makeText(this, msg, Toast.LENGTH_SHORT);
        ts.setGravity(Gravity.CENTER, 0, 0);
        ts.show();
    }

    /**
     * 设置TOP-Activity
     * @param topActivity true:TOP-Activity false:非TOP-Activity
     */
    protected void setTopActivity(boolean topActivity) {
        this.topActivity = topActivity;
    }

    /**
     * 当扫描器连接状态改变时的事件处理
     * @param scanner 扫描器
     * @param state 状态
     */
    @Override
    public void onScannerStatusChanged(CommScanner scanner, CommStatusChangedEvent state) {
        // 当扫描器断开连接时，commScanner将不会连接
        // 由于此事件处理是异步调用的，如果立即将commScanner设为null，可能会在处理过程中导致空指针异常
        // 为防止这种情况，保留实例并使用标志监控连接状态
        final CommConst.ScannerStatus scannerStatus = state.getStatus();
        final String statusStr = "扫描器状态变化: " + scannerStatus.toString();
        logMessage(statusStr);

        if (scanner == commScanner && scannerStatus.equals(CommConst.ScannerStatus.CLOSE_WAIT)) {
            // 当检测到断开连接状态时，终止除TOP屏幕外的所有Activity
            RfidWriteActivity.this.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    try {
                        if(scannerConnected) {
                            // 显示断开连接消息
                            showMessage(MSG_NO_CONNECTION);
                            logMessage("检测到断开连接状态: CLOSE_WAIT");

                            scannerConnected = false;
                            scannerConnectedOnCreate = false;
                            updateConnectionStatus();

                            // 如果不是用户主动断开连接，可以尝试自动重连
                            if (!isConnecting) {
                                String reconnectMsg = "设备连接已断开，正在尝试重新连接...";
                                showMessage(reconnectMsg);
                                logMessage(reconnectMsg);
                                connectToScanner();
                            }
                        }
                    } catch (Exception e) {
                        String errorMsg = "处理断开连接异常: " + e.getClass().getName() + ": " + e.getMessage();
                        logMessage(errorMsg);
                        e.printStackTrace();
                    }
                }
            });
        }
    }

    /**
     * SP1 连接事件回调
     * @param scanner 扫描器实例
     */
    @Override
    public void OnScannerAppeared(CommScanner scanner) {
        boolean successFlag = false;
        try {
            logMessage("扫描器出现回调触发");
            scanner.claim();
            logMessage("扫描器claim成功");
            // 中止连接请求
            CommManager.endAccept();
            CommManager.removeAcceptStatusListener(this);
            logMessage("连接请求已中止，监听器已移除");
            successFlag = true;
        } catch (CommException e) {
            String errorMsg = "扫描器claim异常: " + e.getClass().getName() + ": " + e.getMessage();
            logMessage(errorMsg);
            e.printStackTrace();
        } catch (Exception e) {
            String errorMsg = "扫描器连接其他异常: " + e.getClass().getName() + ": " + e.getMessage();
            logMessage(errorMsg);
            e.printStackTrace();
        }

        try {
            setConnectedCommScanner(scanner);
            logMessage("已设置连接的扫描器");
            final boolean finalSuccessFlag = successFlag;

            // 在UI线程中处理连接结果
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    try {
                        isConnecting = false;
                        if (finalSuccessFlag) {
                            scannerConnectedOnCreate = true;
                            updateConnectionStatus();
                            showMessage(MSG_CONNECT_SUCCESS);
                            logMessage("连接成功，准备设置数据委托并开始扫描");

                            // 设置数据委托并开始扫描
                            try {
                                getCommScanner().getRFIDScanner().setDataDelegate(RfidWriteActivity.this);
                                logMessage("数据委托设置成功");
                                startRfidScan();
                                logMessage("RFID扫描已开始");
                                
                                // 初始化RFID读写帮助类
                                rfidReadWriteHelper = new RFIDReadWriteHelper(getCommScanner());
                                logMessage("RFID读写帮助类已初始化");
                            } catch (Exception e) {
                                String errorMsg = "设置数据委托异常: " + e.getClass().getName() + ": " + e.getMessage();
                                showMessage(MSG_COMMUNICATION_ERROR);
                                logMessage(errorMsg);
                                e.printStackTrace();
                            }
                        } else {
                            // 连接失败，尝试重试
                            logMessage("连接失败，准备重试");
                            handleConnectionFailure();
                        }
                        btnConnect.setEnabled(true);
                    } catch (Exception e) {
                        String errorMsg = "UI更新异常: " + e.getClass().getName() + ": " + e.getMessage();
                        logMessage(errorMsg);
                        btnConnect.setEnabled(true);
                        e.printStackTrace();
                    }
                }
            });
        } catch (Exception e) {
            // 发生异常，尝试重试
            String errorMsg = "设置扫描器异常: " + e.getClass().getName() + ": " + e.getMessage();
            logMessage(errorMsg);
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    isConnecting = false;
                    handleConnectionFailure();
                    btnConnect.setEnabled(true);
                }
            });
            e.printStackTrace();
        }
    }

    /**
     * 检查并请求必要的权限
     * @return 如果已有所有权限返回true，否则返回false
     */
    private boolean checkAndRequestPermissions() {
        if (hasRequiredPermissions()) {
            return true;
        }

        // 收集需要请求的权限
        List<String> permissionsToRequest = new ArrayList<>();

        // 基本权限
        for (String permission : REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                permissionsToRequest.add(permission);
            }
        }

        // Android 12及以上的额外权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            for (String permission : REQUIRED_PERMISSIONS_S) {
                if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                    permissionsToRequest.add(permission);
                }
            }
        }

        // 请求权限
        if (!permissionsToRequest.isEmpty()) {
            ActivityCompat.requestPermissions(
                    this,
                    permissionsToRequest.toArray(new String[0]),
                    PERMISSION_REQUEST_CODE
            );
            return false;
        }

        return true;
    }

    /**
     * 检查是否有所有必要的权限
     * @return 如果有所有必要权限返回true，否则返回false
     */
    private boolean hasRequiredPermissions() {
        // 检查基本权限
        for (String permission : REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }

        // 检查Android 12及以上的额外权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            for (String permission : REQUIRED_PERMISSIONS_S) {
                if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 处理权限请求结果
     */
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;

            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }

            if (allGranted) {
                logMessage("所有权限已授予");
                showMessage("权限已授予，可以连接设备");
            } else {
                logMessage("部分权限被拒绝，可能无法正常连接设备");
                showMessage("缺少必要权限，某些功能可能无法正常工作");
            }
        }
    }
    
    /**
     * 写入标签操作
     */
    private void writeToTag() {
        // 检查设备连接状态
        if (!scannerConnected) {
            showMessage(MSG_NO_CONNECTION);
            logMessage("写入失败：设备未连接");
            return;
        }
        
        // 获取输入内容
        String content = etWriteContent.getText().toString().trim();
        if (content.isEmpty()) {
            showMessage("请输入要写入的内容");
            logMessage("写入失败：内容为空");
            return;
        }
        
        // 显示正在处理的提示
        showMessage("正在处理，请将标签靠近设备...");
        logMessage("开始写入操作，内容：" + content);
        
        // 创建后台线程执行写入操作
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    // 停止当前扫描
                    stopRfidScan();
                    
                    // 执行写入操作
                    boolean success = rfidReadWriteHelper.scanAndWrite(
                            content,
                            RFIDScannerSettings.RFIDBank.USER,  // 使用USER区域
                            (short) 0,                          // 起始地址为0
                            5000                                // 扫描超时时间5秒
                    );
                    
                    // 在UI线程显示结果
                    final String resultMsg = success ? "标签写入成功：" + content : "标签写入失败，请重试";
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showMessage(resultMsg);
                            logMessage(resultMsg);
                            
                            // 如果成功，清空输入框
                            if (success) {
                                etWriteContent.setText("");
                            }
                            
                            // 重新开始扫描
                            startRfidScan();
                        }
                    });
                    
                } catch (Exception e) {
                    final String errorMsg = "写入异常：" + e.getMessage();
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showMessage(errorMsg);
                            logMessage(errorMsg);
                            e.printStackTrace();
                            
                            // 重新开始扫描
                            startRfidScan();
                        }
                    });
                }
            }
        }).start();
    }
}
