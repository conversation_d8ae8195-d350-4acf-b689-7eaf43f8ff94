{"logs": [{"outputFile": "com.example.warehousemanagement.app-mergeDebugResources-37:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c05fa92e389d5803b314d4541ae72f08\\transformed\\core-1.9.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "7563", "endColumns": "100", "endOffsets": "7659"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ff72482a731973c8e0ce199a0d194ac5\\transformed\\appcompat-1.6.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,409,507,613,699,802,919,997,1073,1164,1257,1349,1443,1543,1636,1731,1824,1915,2006,2086,2186,2286,2382,2484,2584,2683,2833,7483", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "404,502,608,694,797,914,992,1068,1159,1252,1344,1438,1538,1631,1726,1819,1910,2001,2081,2181,2281,2377,2479,2579,2678,2828,2924,7558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e8cdd8f14c70e551ad9fcc8ba2fb4e74\\transformed\\material-1.9.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,332,400,475,557,638,727,829,906,970,1055,1117,1175,1260,1323,1385,1443,1509,1571,1626,1722,1779,1838,1894,1961,2066,2146,2227,2356,2429,2500,2582,2633,2684,2750,2816,2889,2970,3038,3111,3182,3249,3334,3401,3488,3576,3650,3718,3803,3854,3918,3998,4080,4142,4206,4269,4364,4453,4538,4629,4684,4739", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,70,67,74,81,80,88,101,76,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,128,72,70,81,50,50,65,65,72,80,67,72,70,66,84,66,86,87,73,67,84,50,63,79,81,61,63,62,94,88,84,90,54,54,75", "endOffsets": "256,327,395,470,552,633,722,824,901,965,1050,1112,1170,1255,1318,1380,1438,1504,1566,1621,1717,1774,1833,1889,1956,2061,2141,2222,2351,2424,2495,2577,2628,2679,2745,2811,2884,2965,3033,3106,3177,3244,3329,3396,3483,3571,3645,3713,3798,3849,3913,3993,4075,4137,4201,4264,4359,4448,4533,4624,4679,4734,4810"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2929,3000,3068,3143,3225,3306,3395,3497,3574,3638,3723,3785,3843,3928,3991,4053,4111,4177,4239,4294,4390,4447,4506,4562,4629,4734,4814,4895,5024,5097,5168,5250,5301,5352,5418,5484,5557,5638,5706,5779,5850,5917,6002,6069,6156,6244,6318,6386,6471,6522,6586,6666,6748,6810,6874,6937,7032,7121,7206,7297,7352,7407", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,70,67,74,81,80,88,101,76,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,128,72,70,81,50,50,65,65,72,80,67,72,70,66,84,66,86,87,73,67,84,50,63,79,81,61,63,62,94,88,84,90,54,54,75", "endOffsets": "306,2995,3063,3138,3220,3301,3390,3492,3569,3633,3718,3780,3838,3923,3986,4048,4106,4172,4234,4289,4385,4442,4501,4557,4624,4729,4809,4890,5019,5092,5163,5245,5296,5347,5413,5479,5552,5633,5701,5774,5845,5912,5997,6064,6151,6239,6313,6381,6466,6517,6581,6661,6743,6805,6869,6932,7027,7116,7201,7292,7347,7402,7478"}}]}]}