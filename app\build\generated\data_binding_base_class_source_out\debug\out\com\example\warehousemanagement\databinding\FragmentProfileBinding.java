// Generated by view binder compiler. Do not edit!
package com.example.warehousemanagement.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.warehousemanagement.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentProfileBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnLogout;

  @NonNull
  public final ImageView ivAvatar;

  @NonNull
  public final LinearLayout llAbout;

  @NonNull
  public final LinearLayout llSettings;

  @NonNull
  public final TextView tvRole;

  @NonNull
  public final TextView tvUsername;

  private FragmentProfileBinding(@NonNull LinearLayout rootView, @NonNull MaterialButton btnLogout,
      @NonNull ImageView ivAvatar, @NonNull LinearLayout llAbout, @NonNull LinearLayout llSettings,
      @NonNull TextView tvRole, @NonNull TextView tvUsername) {
    this.rootView = rootView;
    this.btnLogout = btnLogout;
    this.ivAvatar = ivAvatar;
    this.llAbout = llAbout;
    this.llSettings = llSettings;
    this.tvRole = tvRole;
    this.tvUsername = tvUsername;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentProfileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentProfileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_profile, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentProfileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_logout;
      MaterialButton btnLogout = ViewBindings.findChildViewById(rootView, id);
      if (btnLogout == null) {
        break missingId;
      }

      id = R.id.iv_avatar;
      ImageView ivAvatar = ViewBindings.findChildViewById(rootView, id);
      if (ivAvatar == null) {
        break missingId;
      }

      id = R.id.ll_about;
      LinearLayout llAbout = ViewBindings.findChildViewById(rootView, id);
      if (llAbout == null) {
        break missingId;
      }

      id = R.id.ll_settings;
      LinearLayout llSettings = ViewBindings.findChildViewById(rootView, id);
      if (llSettings == null) {
        break missingId;
      }

      id = R.id.tv_role;
      TextView tvRole = ViewBindings.findChildViewById(rootView, id);
      if (tvRole == null) {
        break missingId;
      }

      id = R.id.tv_username;
      TextView tvUsername = ViewBindings.findChildViewById(rootView, id);
      if (tvUsername == null) {
        break missingId;
      }

      return new FragmentProfileBinding((LinearLayout) rootView, btnLogout, ivAvatar, llAbout,
          llSettings, tvRole, tvUsername);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
