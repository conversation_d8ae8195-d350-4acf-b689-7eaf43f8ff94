{"logs": [{"outputFile": "com.example.warehousemanagement.app-mergeDebugResources-37:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e8cdd8f14c70e551ad9fcc8ba2fb4e74\\transformed\\material-1.9.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,616,705,807,917,1004,1070,1166,1232,1293,1398,1462,1534,1592,1666,1728,1782,1895,1955,2016,2075,2153,2277,2358,2443,2579,2660,2743,2826,2888,2942,3008,3085,3164,3252,3321,3397,3478,3546,3637,3715,3808,3905,3979,4058,4156,4216,4282,4370,4458,4520,4588,4651,4756,4874,4969,5089,5155,5213", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,88,87,81,94,88,101,109,86,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,84,135,80,82,82,61,53,65,76,78,87,68,75,80,67,90,77,92,96,73,78,97,59,65,87,87,61,67,62,104,117,94,119,65,57,83", "endOffsets": "257,346,434,516,611,700,802,912,999,1065,1161,1227,1288,1393,1457,1529,1587,1661,1723,1777,1890,1950,2011,2070,2148,2272,2353,2438,2574,2655,2738,2821,2883,2937,3003,3080,3159,3247,3316,3392,3473,3541,3632,3710,3803,3900,3974,4053,4151,4211,4277,4365,4453,4515,4583,4646,4751,4869,4964,5084,5150,5208,5292"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3072,3161,3249,3331,3426,3515,3617,3727,3814,3880,3976,4042,4103,4208,4272,4344,4402,4476,4538,4592,4705,4765,4826,4885,4963,5087,5168,5253,5389,5470,5553,5636,5698,5752,5818,5895,5974,6062,6131,6207,6288,6356,6447,6525,6618,6715,6789,6868,6966,7026,7092,7180,7268,7330,7398,7461,7566,7684,7779,7899,7965,8023", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,88,87,81,94,88,101,109,86,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,84,135,80,82,82,61,53,65,76,78,87,68,75,80,67,90,77,92,96,73,78,97,59,65,87,87,61,67,62,104,117,94,119,65,57,83", "endOffsets": "307,3156,3244,3326,3421,3510,3612,3722,3809,3875,3971,4037,4098,4203,4267,4339,4397,4471,4533,4587,4700,4760,4821,4880,4958,5082,5163,5248,5384,5465,5548,5631,5693,5747,5813,5890,5969,6057,6126,6202,6283,6351,6442,6520,6613,6710,6784,6863,6961,7021,7087,7175,7263,7325,7393,7456,7561,7679,7774,7894,7960,8018,8102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c05fa92e389d5803b314d4541ae72f08\\transformed\\core-1.9.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "8187", "endColumns": "100", "endOffsets": "8283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ff72482a731973c8e0ce199a0d194ac5\\transformed\\appcompat-1.6.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2247,2360,2470,2587,2754,2865", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2242,2355,2465,2582,2749,2860,2940"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "312,421,532,640,731,838,958,1042,1121,1212,1305,1400,1494,1594,1687,1782,1876,1967,2058,2144,2257,2358,2454,2567,2677,2794,2961,8107", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "416,527,635,726,833,953,1037,1116,1207,1300,1395,1489,1589,1682,1777,1871,1962,2053,2139,2252,2353,2449,2562,2672,2789,2956,3067,8182"}}]}]}