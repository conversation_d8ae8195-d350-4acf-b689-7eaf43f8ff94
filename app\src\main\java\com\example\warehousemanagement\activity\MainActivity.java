package com.example.warehousemanagement.activity;

import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import com.example.warehousemanagement.R;
import com.example.warehousemanagement.databinding.ActivityMainBinding;
import com.example.warehousemanagement.fragment.StorageFragment;
import com.example.warehousemanagement.fragment.ShippingFragment;
import com.example.warehousemanagement.fragment.RecoveryFragment;
import com.example.warehousemanagement.fragment.ProfileFragment;
import com.example.warehousemanagement.rfid.RfidManager;
import com.google.android.material.bottomnavigation.BottomNavigationView;

/**
 * 主Activity
 * 功能：底部导航栏管理，Fragment切换，RFID设备管理
 */
public class MainActivity extends AppCompatActivity {

    private ActivityMainBinding binding;
    private RfidManager rfidManager;

    // Fragment实例
    private StorageFragment storageFragment;
    private ShippingFragment shippingFragment;
    private RecoveryFragment recoveryFragment;
    private ProfileFragment profileFragment;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 初始化
        initViews();
        initRfid();

        // 默认显示入库页面
        showFragment(R.id.nav_storage);
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        // 设置底部导航栏点击事件
        binding.bottomNavigation.setOnItemSelectedListener(item -> {
            showFragment(item.getItemId());
            return true;
        });
    }

    /**
     * 初始化RFID
     */
    private void initRfid() {
        rfidManager = RfidManager.getInstance();
        rfidManager.initialize(this);
    }

    /**
     * 显示指定Fragment
     */
    private void showFragment(int itemId) {
        Fragment fragment = null;
        String tag = null;

        if (itemId == R.id.nav_storage) {
            if (storageFragment == null) {
                storageFragment = new StorageFragment();
            }
            fragment = storageFragment;
            tag = "storage";
            binding.toolbar.setTitle(R.string.tab_storage);
        } else if (itemId == R.id.nav_shipping) {
            if (shippingFragment == null) {
                shippingFragment = new ShippingFragment();
            }
            fragment = shippingFragment;
            tag = "shipping";
            binding.toolbar.setTitle(R.string.tab_shipping);
        } else if (itemId == R.id.nav_recovery) {
            if (recoveryFragment == null) {
                recoveryFragment = new RecoveryFragment();
            }
            fragment = recoveryFragment;
            tag = "recovery";
            binding.toolbar.setTitle(R.string.tab_recovery);
        } else if (itemId == R.id.nav_profile) {
            if (profileFragment == null) {
                profileFragment = new ProfileFragment();
            }
            fragment = profileFragment;
            tag = "profile";
            binding.toolbar.setTitle(R.string.tab_profile);
        }

        if (fragment != null) {
            FragmentManager fragmentManager = getSupportFragmentManager();
            FragmentTransaction transaction = fragmentManager.beginTransaction();
            transaction.replace(R.id.fragment_container, fragment, tag);
            transaction.commit();
        }
    }

    /**
     * 获取RFID管理器
     */
    public RfidManager getRfidManager() {
        return rfidManager;
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 自动连接RFID设备
        if (rfidManager != null) {
            rfidManager.autoConnect();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        // 暂停RFID扫描
        if (rfidManager != null) {
            rfidManager.pauseScanning();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 释放RFID资源
        if (rfidManager != null) {
            rfidManager.release();
        }
        binding = null;
    }
}
