1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.warehousemanagement"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
8-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml
10
11    <!-- 蓝牙权限 -->
12    <uses-permission android:name="android.permission.BLUETOOTH" />
12-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:6:5-68
12-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:6:22-65
13    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
13-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:7:5-74
13-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:7:22-71
14    <!-- Android 12+ 蓝牙权限 -->
15    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
15-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:9:5-76
15-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:9:22-73
16    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
16-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:10:5-73
16-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:10:22-70
17    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
17-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:11:5-78
17-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:11:22-75
18    <!-- 位置权限（蓝牙扫描需要） -->
19    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
19-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:13:5-79
19-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:13:22-76
20    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
20-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:14:5-81
20-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:14:22-78
21    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
21-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:15:5-85
21-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:15:22-82
22    <!-- 本地设备访问权限 -->
23    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
23-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:17:5-80
23-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:17:22-77
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:18:5-81
24-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:18:22-78
25    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
25-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:19:5-82
25-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:19:22-79
26    <!-- 网络权限 -->
27    <uses-permission android:name="android.permission.INTERNET" />
27-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:21:5-67
27-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:21:22-64
28    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
28-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:22:5-79
28-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:22:22-76
29    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
29-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:23:5-76
29-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:23:22-73
30    <!-- 蓝牙设备特性声明 -->
31    <uses-feature
31-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:25:5-87
32        android:name="android.hardware.bluetooth"
32-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:25:19-60
33        android:required="true" />
33-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:25:61-84
34    <uses-feature
34-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:26:5-90
35        android:name="android.hardware.bluetooth_le"
35-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:26:19-63
36        android:required="true" />
36-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:26:64-87
37
38    <permission
38-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03f312cdf143f005647525320abeeeeb\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
39        android:name="com.example.warehousemanagement.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03f312cdf143f005647525320abeeeeb\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03f312cdf143f005647525320abeeeeb\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.example.warehousemanagement.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03f312cdf143f005647525320abeeeeb\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03f312cdf143f005647525320abeeeeb\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
43
44    <application
44-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:28:5-78:19
45        android:name="com.example.warehousemanagement.WarehouseApplication"
45-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:29:9-45
46        android:allowBackup="true"
46-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:30:9-35
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03f312cdf143f005647525320abeeeeb\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
48        android:debuggable="true"
49        android:extractNativeLibs="false"
50        android:icon="@mipmap/ic_launcher"
50-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:31:9-43
51        android:label="@string/app_name"
51-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:32:9-41
52        android:roundIcon="@mipmap/ic_launcher_round"
52-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:33:9-54
53        android:supportsRtl="true"
53-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:34:9-35
54        android:testOnly="true"
55        android:theme="@style/Theme.WarehouseManagement" >
55-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:35:9-57
56
57        <!-- 登录Activity -->
58        <activity
58-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:38:9-45:20
59            android:name="com.example.warehousemanagement.activity.LoginActivity"
59-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:39:13-51
60            android:exported="true" >
60-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:40:13-36
61            <intent-filter>
61-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:41:13-44:29
62                <action android:name="android.intent.action.MAIN" />
62-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:42:17-69
62-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:42:25-66
63
64                <category android:name="android.intent.category.LAUNCHER" />
64-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:43:17-77
64-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:43:27-74
65            </intent-filter>
66        </activity>
67        <activity
67-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:47:9-49:40
68            android:name="com.example.warehousemanagement.activity.MainActivity"
68-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:48:13-50
69            android:exported="false" />
69-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:49:13-37
70
71        <!-- 添加记录的Activity -->
72        <activity
72-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:52:9-55:67
73            android:name="com.example.warehousemanagement.activity.StorageAddActivity"
73-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:53:13-56
74            android:exported="false"
74-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:54:13-37
75            android:parentActivityName="com.example.warehousemanagement.activity.MainActivity" />
75-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:55:13-64
76        <activity
76-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:57:9-60:67
77            android:name="com.example.warehousemanagement.activity.ShippingAddActivity"
77-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:58:13-57
78            android:exported="false"
78-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:59:13-37
79            android:parentActivityName="com.example.warehousemanagement.activity.MainActivity" />
79-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:60:13-64
80        <activity
80-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:62:9-65:67
81            android:name="com.example.warehousemanagement.activity.RecoveryAddActivity"
81-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:63:13-57
82            android:exported="false"
82-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:64:13-37
83            android:parentActivityName="com.example.warehousemanagement.activity.MainActivity" />
83-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:65:13-64
84
85        <!-- 设置Activity -->
86        <activity
86-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:68:9-71:67
87            android:name="com.example.warehousemanagement.activity.SettingsActivity"
87-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:69:13-54
88            android:exported="false"
88-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:70:13-37
89            android:parentActivityName="com.example.warehousemanagement.activity.MainActivity" />
89-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:71:13-64
90
91        <!-- 数据同步服务 -->
92        <service
92-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:74:9-77:40
93            android:name="com.example.warehousemanagement.services.DataSyncService"
93-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:75:13-53
94            android:enabled="true"
94-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:76:13-35
95            android:exported="false" />
95-->C:\Users\<USER>\Desktop\Box\app\src\main\AndroidManifest.xml:77:13-37
96
97        <provider
97-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
98            android:name="androidx.startup.InitializationProvider"
98-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
99            android:authorities="com.example.warehousemanagement.androidx-startup"
99-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
100            android:exported="false" >
100-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
101            <meta-data
101-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.emoji2.text.EmojiCompatInitializer"
102-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
103                android:value="androidx.startup" />
103-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41a226a0f789e512d18daa7c0c70d57e\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
104            <meta-data
104-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ab6b896c30a65e2f99f3a0f6b17306d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
105-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ab6b896c30a65e2f99f3a0f6b17306d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
106                android:value="androidx.startup" />
106-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ab6b896c30a65e2f99f3a0f6b17306d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
107            <meta-data
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
108                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
109                android:value="androidx.startup" />
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
110        </provider>
111
112        <receiver
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
113            android:name="androidx.profileinstaller.ProfileInstallReceiver"
113-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
114            android:directBootAware="false"
114-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
115            android:enabled="true"
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
116            android:exported="true"
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
117            android:permission="android.permission.DUMP" >
117-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
119                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
120            </intent-filter>
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
122                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
123            </intent-filter>
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
125                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
125-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
125-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
126            </intent-filter>
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
128                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
128-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
128-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dcac9590e92f4c86b77aadcf2c56567\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
129            </intent-filter>
130        </receiver>
131    </application>
132
133</manifest>
