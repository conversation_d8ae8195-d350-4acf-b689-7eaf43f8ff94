<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent">#FFFF4081</color>
    <color name="background">#FFF5F5F5</color>
    <color name="background_light">#FFFAFAFA</color>
    <color name="black">#FF000000</color>
    <color name="button_disabled">#FFBDBDBD</color>
    <color name="button_primary">#FF2196F3</color>
    <color name="button_secondary">#FF757575</color>
    <color name="card_background">#FFFFFFFF</color>
    <color name="divider">#FFE0E0E0</color>
    <color name="edit_text_background">#FFFFFFFF</color>
    <color name="edit_text_border">#FFE0E0E0</color>
    <color name="edit_text_border_focused">#FF2196F3</color>
    <color name="error">#FFF44336</color>
    <color name="error_light">#FFEF5350</color>
    <color name="info">#FF2196F3</color>
    <color name="primary">#FF2196F3</color>
    <color name="primary_dark">#FF1976D2</color>
    <color name="primary_light">#FFBBDEFB</color>
    <color name="success">#FF4CAF50</color>
    <color name="success_light">#FF81C784</color>
    <color name="surface">#FFFFFFFF</color>
    <color name="tab_selected">#FF2196F3</color>
    <color name="tab_unselected">#FF757575</color>
    <color name="text_hint">#FFBDBDBD</color>
    <color name="text_primary">#FF212121</color>
    <color name="text_secondary">#FF757575</color>
    <color name="text_white">#FFFFFFFF</color>
    <color name="warning">#FFFF9800</color>
    <color name="warning_light">#FFFFCC02</color>
    <color name="white">#FFFFFFFF</color>
    <string name="about">关于</string>
    <string name="all_types">全部类型</string>
    <string name="app_name">仓库管理系统</string>
    <string name="auto_connect">自动连接</string>
    <string name="auto_sync">自动同步</string>
    <string name="bag_expiry_days">袋子保质期(天)</string>
    <string name="box_expiry_days">箱子保质期(天)</string>
    <string name="cancel">取消</string>
    <string name="confirm">确认</string>
    <string name="connection_status">连接状态</string>
    <string name="connection_timeout">连接超时</string>
    <string name="customer_name">客户名称</string>
    <string name="customer_name_hint">请输入客户名称</string>
    <string name="data_settings">数据设置</string>
    <string name="delete">删除</string>
    <string name="disconnected">未连接</string>
    <string name="edit">编辑</string>
    <string name="error_light">错误浅色</string>
    <string name="expiry_days">保质期</string>
    <string name="filter">筛选</string>
    <string name="item_type">物品类型</string>
    <string name="item_type_bag">袋子</string>
    <string name="item_type_box">箱子</string>
    <string name="loading">加载中...</string>
    <string name="login_button">登录</string>
    <string name="login_error">用户名或密码错误</string>
    <string name="login_success">登录成功</string>
    <string name="login_title">用户登录</string>
    <string name="logout">退出登录</string>
    <string name="need_recovery">是否需要回收</string>
    <string name="network_error">网络错误</string>
    <string name="no">否</string>
    <string name="no_data">暂无数据</string>
    <string name="no_scanned_tags">暂无扫描到的标签</string>
    <string name="notification_enabled">消息通知</string>
    <string name="operation_failed">操作失败</string>
    <string name="operation_success">操作成功</string>
    <string name="password_hint">请输入密码</string>
    <string name="profile">个人中心</string>
    <string name="profile_title">个人中心</string>
    <string name="recovery">回收</string>
    <string name="recovery_add">添加回收</string>
    <string name="recovery_date">回收日期</string>
    <string name="recovery_days">回收提醒天数</string>
    <string name="recovery_info">回收信息</string>
    <string name="recovery_title">回收管理</string>
    <string name="refresh">刷新</string>
    <string name="remaining_days">剩余天数</string>
    <string name="remark1">备注1</string>
    <string name="remark2">备注2</string>
    <string name="rfid_connected">RFID设备已连接</string>
    <string name="rfid_connecting">正在连接RFID设备...</string>
    <string name="rfid_disconnected">RFID设备已断开</string>
    <string name="rfid_scan_area">RFID扫描区域</string>
    <string name="rfid_scan_start">开始扫描</string>
    <string name="rfid_scan_stop">停止扫描</string>
    <string name="rfid_settings">RFID设置</string>
    <string name="save">保存</string>
    <string name="scan_distance">扫描距离(米)</string>
    <string name="scan_power">扫描功率</string>
    <string name="scan_tags">扫描标签</string>
    <string name="scanned_tags">已扫描标签</string>
    <string name="search">搜索</string>
    <string name="server_settings">服务器设置</string>
    <string name="server_url">服务器地址</string>
    <string name="settings">设置</string>
    <string name="shipping">发货</string>
    <string name="shipping_add">添加发货</string>
    <string name="shipping_date">发货日期</string>
    <string name="shipping_title">发货管理</string>
    <string name="start_scan">开始扫描</string>
    <string name="storage">入库</string>
    <string name="storage_add">添加入库</string>
    <string name="storage_date">入库日期</string>
    <string name="storage_remark">备注</string>
    <string name="storage_title">入库管理</string>
    <string name="sync_interval">同步间隔</string>
    <string name="system_settings">系统设置</string>
    <string name="tab_profile">我的</string>
    <string name="tab_recovery">回收</string>
    <string name="tab_shipping">发货</string>
    <string name="tab_storage">入库</string>
    <string name="tag_already_exists">标签已存在</string>
    <string name="tag_id">标签ID</string>
    <string name="tag_not_found">标签不存在</string>
    <string name="used_days">已使用天数</string>
    <string name="username_hint">请输入用户名</string>
    <string name="warning_light">警告浅色</string>
    <string name="yes">是</string>
    <style name="Base.Theme.WarehouseManagement" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
    </style>
    <style name="BodyText">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:layout_marginBottom">4dp</item>
    </style>
    <style name="BodyTextStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>
    <style name="CardStyle" parent="Widget.Material3.CardView.Elevated">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">8dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="cardBackgroundColor">@color/card_background</item>
    </style>
    <style name="EditTextStyle" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">16dp</item>
        <item name="boxStrokeColor">@color/edit_text_border_focused</item>
        <item name="hintTextColor">@color/text_hint</item>
    </style>
    <style name="LoginTheme" parent="Theme.WarehouseManagement">
        <item name="android:windowBackground">@color/primary_light</item>
    </style>
    <style name="PrimaryButton" parent="Widget.Material3.Button">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textColor">@color/text_white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="backgroundTint">@color/button_primary</item>
    </style>
    <style name="SecondaryButton" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textColor">@color/button_secondary</item>
        <item name="android:textSize">16sp</item>
        <item name="strokeColor">@color/button_secondary</item>
    </style>
    <style name="SubtitleText">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style>
    <style name="SubtitleTextStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>
    <style name="Theme.WarehouseManagement" parent="Base.Theme.WarehouseManagement"/>
    <style name="TitleText">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_marginBottom">12dp</item>
    </style>
    <style name="TitleTextStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>
</resources>